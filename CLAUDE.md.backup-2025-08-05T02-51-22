# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SkillHub is a comprehensive educational platform built with Ruby on Rails 7 backend and Vue.js 2.7 frontend. The application supports multi-tenancy through school management, with features including courses, exams, lessons, user management, chat functionality, and AI integration.

## Architecture

### Backend (Rails)
- **Ruby Version**: 3.1.2
- **Rails Version**: *******
- **Database**: MySQL 8.0
- **Background Jobs**: Sidekiq 5.2.9
- **Real-time**: ActionCable with AnyCable
- **Authentication**: Devise with OAuth support
- **Authorization**: CanCanCan
- **API**: RESTful with Doorkeeper OAuth, Swagger documentation

### Frontend
- **Framework**: Vue.js 2.7.8 with TypeScript
- **Build Tool**: Webpack 5
- **State Management**: Vuex
- **UI Components**: Vuetify 2.6.7, UIV
- **Real-time**: ActionCable integration

### Core Models & Architecture
- **Multi-tenancy**: `SchoolManager` handles school-based routing and context
- **User Management**: Devise-based authentication with role-based access
- **Educational Content**: `Course` → `Lesson` → `Question` hierarchy
- **Assessment**: `Exam` → `UserExam` → `Assessment` flow
- **AI Integration**: ChatGPT integration for content generation and assessment

### Key Services
- **SchoolManager**: Handles multi-tenant routing and school context
- **ChatGPT Integration**: AI-powered content generation and evaluation
- **File Management**: CarrierWave with AWS S3 integration
- **Payment Processing**: Stripe integration

## Development Commands

### Rails Backend
```bash
cd rails

# Development server
bundle exec rails server

# Database setup
bundle exec rake db:create
bundle exec rake db:migrate
bundle exec rake db:seed

# Background jobs
bundle exec sidekiq

# Tests
bundle exec rspec
bundle exec rspec --only-failures

# Deployment
bundle exec cap staging deploy
bundle exec cap production deploy

# Console
bundle exec rails console
```

### Frontend
```bash
cd frontend

# Install dependencies
yarn install

# Development build (watch mode)
yarn watch

# Production build
yarn build

# Linting
yarn lint
```

### Docker Development
```bash
# Start all services
docker-compose up

# Build and start
docker-compose up --build

# Run specific service
docker-compose run web bundle exec rails console
```

### Testing
```bash
# E2E Tests
cd e2e_test
yarn run_test                    # Run Cypress tests
yarn run_test_staging           # Run staging tests
yarn open_test                  # Open Cypress GUI
```

## Database & Models

### Core Entities
- **School**: Multi-tenant organization unit
- **User**: Authentication and user management (Devise)
- **Course**: Educational content container
- **Lesson**: Individual learning units
- **Exam**: Assessment mechanism
- **Question (Q)**: Individual questions within exams
- **UserExam**: User's exam attempt tracking
- **Assessment**: Evaluation and scoring
- **Enrollment**: Course enrollment tracking

### Key Relationships
- Schools have many courses, users, exams
- Courses contain lessons and can have exams
- Users can be enrolled in courses and take exams
- Assessments evaluate user performance on questions

## File Structure

### Rails App Structure
- `app/lib/school_manager.rb`: Core multi-tenancy logic
- `app/models/`: ActiveRecord models with business logic
- `app/controllers/`: API and web controllers
- `app/services/`: Business logic services
- `app/serializers/`: API response serialization
- `app/workers/`: Background job processors
- `config/routes.rb`: API and web routing

### Frontend Structure
- `frontend/src/packs/application.ts`: Main entry point
- `frontend/src/apps/`: Vue.js applications
- `frontend/src/helpers/`: Utility functions
- `frontend/webpack/`: Build configuration

## Key Configuration Files

- `rails/Gemfile`: Ruby dependencies
- `frontend/package.json`: Node.js dependencies
- `docker-compose.yml`: Development environment setup
- `rails/config/database.yml`: Database configuration
- `rails/config/routes.rb`: Application routing

## Development Workflow

1. **Backend Changes**: Modify Rails code, run tests with `rspec`
2. **Frontend Changes**: Edit Vue.js code, build with `yarn watch`
3. **Database Changes**: Create migrations, run `rake db:migrate`
4. **Testing**: Use `rspec` for backend, Cypress for E2E testing
5. **Deployment**: Use Capistrano for staging/production deployment

## Important Notes

- The application uses a custom `SchoolManager` for multi-tenancy
- Frontend assets are compiled into `rails/app/assets/`
- Docker setup available for local development
- Comprehensive test coverage expected for new features
- API versioning follows `/api/v1/` and `/api/v2/` patterns