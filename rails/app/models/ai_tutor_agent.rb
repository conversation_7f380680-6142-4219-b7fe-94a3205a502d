class AiTutorAgent < ApplicationRecord
  belongs_to :school

  # Agent types based on the UI mockup (kept for backward compatibility)
  AGENT_TYPES = %w[dashboard lesson goal exam].freeze

  # Agent categories for flexible classification
  AGENT_CATEGORIES = %w[lesson dashboard goal exam custom].freeze

  # Training statuses
  TRAINING_STATUSES = %w[not_trained training trained failed].freeze

  # Associations
  has_many :ai_tutor_prompts, dependent: :destroy
  has_many :ai_tutor_tools, dependent: :destroy
  has_many :ai_tutor_rag_configs, dependent: :destroy
  has_many :ai_tutor_sources, dependent: :destroy
  has_many :courses, dependent: :nullify, foreign_key: :default_ai_tutor_agent_id

  # Image upload
  mount_uploader :image, ImageUploader

  # Validations
  validates :agent_type, presence: true
  validates :name, presence: true
  validates :enabled, inclusion: { in: [true, false] }
  validates :training_status, inclusion: { in: TRAINING_STATUSES }
  validates :agent_category, inclusion: { in: AGENT_CATEGORIES }
  validates :is_default, inclusion: { in: [true, false] }

  # Only one default agent per school
  validates :is_default, uniqueness: { scope: :school_id }, if: :is_default?

  # Scopes
  scope :enabled, -> { where(enabled: true) }
  scope :by_type, ->(type) { where(agent_type: type) }
  scope :by_category, ->(category) { where(agent_category: category) }
  scope :default_agents, -> { where(is_default: true) }
  scope :custom_agents, -> { where(agent_category: 'custom') }
  scope :not_trained, -> { where(training_status: 'not_trained') }
  scope :trained, -> { where(training_status: 'trained') }
  scope :training, -> { where(training_status: 'training') }
  scope :training_failed, -> { where(training_status: 'failed') }

  def icon_class
    case agent_category
    when 'dashboard'
      'bi-speedometer2'
    when 'lesson'
      'bi-book'
    when 'goal'
      'bi-target'
    when 'exam'
      'bi-clipboard-check'
    when 'custom'
      'bi-person-circle'
    else
      'bi-robot'
    end
  end

  def color_class
    case agent_category
    when 'dashboard'
      'primary'
    when 'lesson'
      'success'
    when 'goal'
      'warning'
    when 'exam'
      'info'
    when 'custom'
      'purple'
    else
      'secondary'
    end
  end

  def avatar_url(size = 40)
    if image.present?
      image.url
    else
      "/assets/ai-tutor-#{agent_category}-avatar.png"
    end
  end

  def display_name
    if agent_category == 'custom'
      name
    else
      "#{name} (#{agent_category.humanize})"
    end
  end

  def configured?
    ai_tutor_prompts.enabled.exists? ||
    ai_tutor_tools.enabled.exists? ||
    ai_tutor_rag_configs.enabled.exists? ||
    ai_tutor_sources.enabled.exists?
  end

  def configuration_summary
    {
      prompts_count: ai_tutor_prompts.enabled.count,
      tools_count: ai_tutor_tools.enabled.count,
      rag_enabled: ai_tutor_rag_configs.enabled.exists?,
      sources_count: ai_tutor_sources.enabled.count
    }
  end

  def sources_summary
    AiTutorSource.total_by_type(self)
  end

  def sources_usage_stats
    AiTutorSource.usage_stats(self)
  end

  def can_train?
    enabled? && !training? && has_trainable_sources?
  end

  def training?
    training_status == 'training'
  end

  def trained?
    training_status == 'trained'
  end

  def training_failed?
    training_status == 'failed'
  end

  def has_trainable_sources?
    ai_tutor_sources.any?(&:can_train?)
  end

  def start_training!
    update!(
      training_status: 'training',
      training_error: nil
    )
  end

  def complete_training!
    update!(
      training_status: 'trained',
      last_trained_at: Time.current,
      training_error: nil
    )
  end

  def fail_training!(error_message)
    update!(
      training_status: 'failed',
      training_error: error_message
    )
  end

  def training_summary
    all_text_sources = AiTutorTextSource.joins(:ai_tutor_source)
                                       .where(ai_tutor_source: { ai_tutor_agent_id: id })

    {
      agent: {
        training_status: training_status,
        last_trained_at: last_trained_at,
        training_error: training_error
      },
      sources: {
        total: ai_tutor_sources.count,
        not_trained: ai_tutor_sources.not_trained.count,
        training: ai_tutor_sources.training.count,
        trained: ai_tutor_sources.trained.count,
        failed: ai_tutor_sources.training_failed.count
      },
      content_pieces: {
        total: all_text_sources.count,
        not_trained: all_text_sources.not_trained.count,
        training: all_text_sources.training.count,
        trained: all_text_sources.trained.count,
        failed: all_text_sources.training_failed.count
      },
      last_activity: {
        last_source_trained: ai_tutor_sources.joins(:ai_tutor_text_sources)
                                           .where(ai_tutor_text_sources: { training_status: 'trained' })
                                           .maximum('ai_tutor_text_sources.trained_at'),
        last_training_attempt: [last_trained_at,
                               ai_tutor_sources.maximum(:updated_at)].compact.max
      }
    }
  end

  def all_sources_trained?
    ai_tutor_sources.any? && ai_tutor_sources.all? { |source| source.all_text_sources_trained? }
  end

  def any_sources_training?
    ai_tutor_sources.any? { |source| source.any_text_sources_training? }
  end

  def content_size_stats
    stats = {}

    text_size = ai_tutor_sources.where(source_type: 'text')
                               .joins(:ai_tutor_text_sources)
                               .sum('LENGTH(ai_tutor_text_sources.content)')
    stats['text'] = text_size if text_size > 0

    web_size = ai_tutor_sources.where(source_type: 'web')
                              .joins(:ai_tutor_text_sources)
                              .sum('LENGTH(ai_tutor_text_sources.content)')
    stats['web'] = web_size if web_size > 0

    qa_size = ai_tutor_sources.where(source_type: 'qa')
                             .joins(:ai_tutor_qa_source)
                             .sum('LENGTH(COALESCE(ai_tutor_qa_sources.answer, "")) + LENGTH(COALESCE(ai_tutor_qa_sources.questions, "[]"))')
    stats['qa'] = qa_size if qa_size > 0

    file_size = ai_tutor_sources.where(source_type: 'file')
                               .joins(:ai_tutor_file_source)
                               .sum('ai_tutor_file_sources.file_size')
    stats['file'] = file_size if file_size > 0

    notion_count = ai_tutor_sources.where(source_type: 'notion').count
    if notion_count > 0
      stats['notion'] = 0
    end

    stats
  end

  def total_content_size
    content_size_stats.values.sum
  end

  def sources_count_by_type
    ai_tutor_sources.group(:source_type).count
  end
end
