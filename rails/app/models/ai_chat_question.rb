class AiChatQuestion < ApplicationRecord
  DEFAULT_COUNT = 4

  has_many :ai_chat_question_users
  has_many :users, through: :ai_chat_question_users
  has_many :ai_chat_questionables, dependent: :destroy
  has_many :lessons, through: :ai_chat_questionables, source: :questionable, source_type: "Lesson"

  mount_uploader :image, ImageUploader
  mount_uploader :icon, QuestionIconUploader

  validates :title, presence: true
  validates :prompt, presence: true
  validates :published_at, presence: true
  validates :default, inclusion: { in: [true, false] }

  enum :context, { lesson: 1, question: 2, goal: 3, dashboard: 4 }

  scope :published, -> { where("published_at <= ?", Time.current) }

  include RankedModel
  ranks :row_order

  def published?
    published_at.present? && published_at < Time.current
  end

  def clicks_count
    ai_chat_question_users.count
  end
end
