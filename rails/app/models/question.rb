class Question < ApplicationRecord
  belongs_to :category
  belongs_to :user
  belongs_to :school
  acts_as_taggable

  has_many :question_relatives, dependent: :destroy
  has_many :goals, through: :question_relatives, source: :relativeable, source_type: "Goal"
  has_many :courses, through: :question_relatives, source: :relativeable, source_type: "Course"
  has_many :lessons, through: :question_relatives, source: :relativeable, source_type: "Lesson"
  has_many :votes, as: :votable, dependent: :destroy
  has_many :answers, dependent: :destroy
  has_many :answers_votes, through: :answers, source: :votes
  has_many :answers_comments, through: :answers, source: :comments
  has_many :notifications, as: :target, dependent: :destroy
  has_many :comments, as: :commentable, dependent: :destroy
  has_many :answers_notifications, through: :answers, source: :notifications
  has_many :comments_notifications, through: :comments, source: :notifications
  has_many :answers_comments_notifications, through: :answers_comments, source: :notifications
  has_many :votes_notifications, through: :votes, source: :notifications
  has_many :answers_votes_notifications, through: :answers_votes, source: :notifications
  has_many_attached :materials

  scope :latest, ->{ order(created_at: :desc) }
  scope :answered, ->{ joins(:answers).where(answers: { accepted: true }) }
  scope :not_accepted, ->{ joins(:answers).where(answers: { accepted: false }) }
  scope :relative, ->{ order(created_at: :desc) } # 人気がある質問を設定したほうがいい
  scope :same_url_with, ->(url) { where(from: url) }
  scope :shared, ->{ where.not(question_type: :private_question) }
  scope :filter_by_query, ->(query) {
    patterns = query.to_s.strip.split(/[ ,　]/)
    sql_body = ""
    patterns.each do |pattern|
      sql_body += " AND " unless sql_body.blank?
      sql_body += "(questions.title LIKE '%#{sanitize_sql_like(pattern)}%' \
        OR questions.body LIKE '%#{sanitize_sql_like(pattern)}%')"
    end

    where(sql_body)
  }

  after_create :save_activity
  after_create_commit :notify_to_managers
  after_create_commit :notify_to_admin_and_manager
  after_create_commit :create_mail_notification

  validates :title, length: { maximum: Settings.max_length.name }, presence: true
  validates :body, length: { maximum: Settings.max_length.body }, presence: true

  enum question_type: { nomal_question: 0, private_question: 1 }

  def set_question_relatives_from_url
    path = self.from.gsub(/^.+?classroom\//, "")
    path.split("/").each_slice(2) do |context, value|
      if defined?(context.classify.constantize) && value.present? && (models = context.classify.constantize.where(id: value).first.try(:get_contexts))
        models.each do |model|
          self.send("#{model.class.name}s".downcase.to_sym) << model
        end
      end
    end
  end

  def set_question_relatives(course_id, lesson_id)
    self.question_relatives.create(relativeable_type: "Course", relativeable_id: course_id) if course_id.present?
    self.question_relatives.create(relativeable_type: "Lesson", relativeable_id: lesson_id) if lesson_id.present?
  end

  def set_school_from_path
    path = self.from.gsub(/^.+?classroom\//,"")
    splitted_path = path.split("/")
    object = splitted_path[0].classify.constantize.find(splitted_path[1])

    case object.class.name
    when "Enrollment"
      object.course.school
    when "Course"
      object.school
    when "Goal"
      object.school
    end
  end

  def best_answer
    self.answers.accepted.first.present? ? self.answers.first : nil
  end

  def answered?
    self.answers.accepted.first.present?
  end

  def vote_values
    self.votes.sum(:value)
  end

  def notify_to_managers
    NotifyManagersJob.perform_later(self.id)
  end

  def notify_to_managers_execute
    notified_user_ids = []

    # For school Manager
    self.school.admin_and_school_managers.each do |manager_user|
      next if notified_user_ids.include?(manager_user.id)
      notify_to_manager(manager_user)
      notified_user_ids.push(manager_user.id)
    end

    # For question Manager
    self.school.question_partners.each do |manager_user|
      next if notified_user_ids.include?(manager_user.id)
      notify_to_manager(manager_user)
      notified_user_ids.push(manager_user.id)
    end

    # For course + Goal Manager
    (courses + goals).each do |relative|
      relative.managers_users.each do |manager_user|
        next if notified_user_ids.include?(manager_user.id)
        notify_to_manager(manager_user)
        notified_user_ids.push(manager_user.id)
      end
    end
  end

  def notify_to_manager(manager)
    manager.notifications.create target: self, action: "ask"
    QuestionMailer.notify(manager, self).deliver_later
  end

  def notify_to_admin_and_manager
    NotifyAdminAndManagerJob.perform_later(self.id)
  end

  def notify_to_admin_and_manager_execute
    User.admins.each do |admin|
      admin.notifications.create target: self, action: "ask", school_id: self.school.id
    end

    self.school.admin_and_school_managers.each do |user|
      user.notifications.create target: self, action: "ask", school_id: self.school.id
    end
  end

  def save_activity
    Activity.create user: self.user, action: "ask", object: self, school: self.school, goal: self.goal
  end

  def goal
    self.goals.first
  end

  def create_mail_notification
    QuestionNotificationMailWorker.perform_async(self.id)
  end

  def answers_count
    self.answers.size
  end

  def created_at_nichiji
    self.created_at&.strftime("%Y年%m月%d日 %H時%M分")
  end
end
