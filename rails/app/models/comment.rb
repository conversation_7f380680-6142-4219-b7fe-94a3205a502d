class Comment < ApplicationRecord
  attr_accessor :skip_callback
  belongs_to :user
  belongs_to :commentable, polymorphic: true
  has_many :notifications, as: :target, dependent: :destroy

  scope :latest, ->{order(created_at: :desc)}

  after_create_commit :notify_to_users, unless: :skip_callback
  after_create_commit :notify_to_admin_managers, unless: :skip_callback
  after_create_commit :save_activity, unless: :skip_callback
  after_create_commit :create_mail_notify_comment_inquiry, unless: :skip_callback
  after_create_commit :create_mail_notify_comment_question, unless: :skip_callback

  def save_activity
    Activity.create user: self.user, action: "comment", object: self, school: self.commentable.try(:school), goal: self.commentable.try(:goal)
  end

  def notify_to_users
    users = self.commentable.try(:user) ? [self.commentable.user] : []
    users |= self.commentable.comments.includes(:user).map(&:user)
    comments_notification_target = self.commentable.try(:comments_notification_target)
    users |= comments_notification_target if comments_notification_target.present?

    school = self.commentable.try(:school)

    if users.present? && school.present?
      users.each do |user|
        next if user == self.user
        next if user.nil?
        user.notifications.create action: "comment", target: self, school_id: school.id
      end
    end
  end

  def notify_to_admin_managers
    school = self.commentable.try(:school)
    if school.present?
      school.admin_and_school_managers.each do |user|
        user.notifications.create action: "comment", target: self, school_id: school.id
      end
    end
  end

  def create_mail_notify_comment_inquiry
    return if inquiry.blank?

    InquiryCommentNotificationMailWorker.perform_async(inquiry.id, self.user.name, self.id)
  end

  def create_mail_notify_comment_question
    return if question.blank?

    QuestionCommentNotificationMailWorker.perform_async(question.id, self.user.name, self.id)
  end

  def inquiry
    return nil if self.commentable_type != "Inquiry"
    Inquiry.where(id:  self.commentable_id).first
  end

  def question
    return nil if self.commentable_type != "Question"
    Question.where(id:  self.commentable_id).first
  end

  def created_at_nichiji
    self.created_at&.strftime("%Y年%m月%d日 %H時%M分")
  end

  def send_to_chatgpt
    return if skip_callback
    if self.commentable_type == "Question"
      comment = Comment.create(
        user_id: self.question.school.teachers.first.user_id,
        commentable_id: self.commentable_id,
        commentable_type: self.commentable_type,
        body: "",
        skip_callback: true,
        from_chatgpt: true,
        to_user_id: self.user_id,
        reply_to_comment_id: self.id
      )
      channel = "user:#{self.user_id}:#{self.commentable_id}:chats"
      ActionCable.server.broadcast channel, { data: "Done"}
      messages = get_converions self.question, comment
      SendChatSteamJob.perform_later(messages, comment)
    end
  end

  def get_converions question, comment
    messages = [
          {"role": "system", "content": "あなたはBotです。ユーザの質問に答えています。"},
        ]
    content = ""
    if question.lessons.size > 0
      content = question.lessons.first.md_body
      content += "上記の内容について、以下の質問をします。"
    elsif question.courses.size > 0
      content = question.courses.first.body
      content += "上記の内容について、以下の質問をします。"
    end
    content += question&.body
    messages.push({ role: "user", content: content})
    question.comments.order(created_at: :asc).each do |c|
      if c != comment
        if c.from_chatgpt
          messages.push({ role: "assistant", content: c.body})
        else
          messages.push({ role: "user", content: c.body})
        end
      end
    end
    return messages
  end

  def send_each_chatgpt
    # channel = "user:#{self.to_user_id}:#{self.commentable_id}:chats"
    # ActionCable.server.broadcast channel, {comment_id: self.id, content: self.body}
  end
end
