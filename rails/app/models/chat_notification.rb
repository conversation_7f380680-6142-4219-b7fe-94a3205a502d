class ChatNotification < ApplicationRecord
  belongs_to :chat_room
  belongs_to :user

  after_save :stop_send_notification_email, if: ->{saved_change_to_unread_count? && self.readed?}
  after_update_commit {ChatNotificationBroadcastJob.perform_now self.user}
  scope :unread, ->{where.not unread_count: 0}
  scope :for, ->(user){where user: user}
  scope :by, ->(chat_room){where chat_room: chat_room}
  scope :latest, ->{order updated_at: :desc}

  def mark_as_read
    self.unread_count = 0
    self
  end

  def increase_unread
    self.unread_count += 1
    self
  end

  def readed?
    unread_count == 0
  end

  def latest_chat
    chat_room.chats.latest.first
  end

  def send_notification_email chat
    job = notification_email_queue.first
    if job.present?
      notification_at = Time.at(job.score)
      chat_ids = job.args[0]["arguments"][2]
      job.delete
    else
      chat_ids = []
      notification_at = 10.minutes.from_now
    end
    chat_ids << chat.id
    SendChatNotificationEmailJob.set(wait_until: notification_at).perform_now(self.user_id, self.chat_room_id, chat_ids)
  end

  def stop_send_notification_email
    notification_email_queue.each(&:delete)
  end

  def notification_email_queue
    queue = Sidekiq::ScheduledSet.new
    job = queue.select do |job|
      job.item["wrapped"] == "SendChatNotificationEmailJob" && job.args[0]["arguments"][0..1] == [self.user_id, self.chat_room_id]
    end
  end
end
