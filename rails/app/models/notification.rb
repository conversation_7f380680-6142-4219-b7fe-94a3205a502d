class Notification < ApplicationRecord
  belongs_to :user
  belongs_to :target, polymorphic: true

  after_create_commit { NotificationBroadcastJob.perform_now self.user, self }
  scope :unread, ->{ joins(:user).where("notifications.created_at > users.last_check_notification OR users.last_check_notification IS NULL") }
  scope :latest, ->{ order(id: :desc) } # We only save records from rails, not import => can use id. use created_at need indexing.
  scope :by_user, ->(user){ where user: user }

  def mark_as_read
    self.readed_at = Time.now
    self.save
  end

  def unread?
    self.readed_at.nil?
  end
end
