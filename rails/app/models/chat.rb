class Chat < ApplicationRecord
  belongs_to :chat_room
  belongs_to :user

  paginates_per 10

  after_create_commit {CreateNotificationJob.perform_now self}
  after_create_commit {MessageBroadcastJob.perform_now self}

  scope :latest, ->{order(created_at: :desc)}

  after_create :save_activity

  def save_activity
    Activity.create user: self.user, action: "chat", object: self, school: self.chat_room.chatable.school
  end
end
