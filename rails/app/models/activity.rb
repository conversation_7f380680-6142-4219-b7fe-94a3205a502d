class Activity < ApplicationRecord
  belongs_to :user
  belongs_to :school
  belongs_to :goal
  belongs_to :object, polymorphic: true

  scope :filter_by_school, ->(school) {where(school_id: school.id)}
  scope :for_users, ->(users){includes(:object).where(user: users)}
  scope :latest, ->{order created_at: :desc}
  scope :recent, ->{order created_at: :desc}

  after_create_commit {ActivitiesBroadcastJob.perform_now self unless action == "login"}
  after_create_commit :create_user_tracking

  def create_user_tracking
    Tracking::User.update_base(self.user.id, self.school.id, Time.zone.now)
  end

  def description
    case action
    when "login"
      "ログイン"
    when "show_lesson"
      "レッスン「#{object&.name || object&.title || 'Unknown'}」を閲覧"
    when "finish_lesson"
      "レッスン「#{object&.name || object&.title || 'Unknown'}」を完了"
    when "show_exam"
      "試験「#{object&.name || object&.title || 'Unknown'}」を閲覧"
    when "finish_exam"
      "試験「#{object&.name || object&.title || 'Unknown'}」を完了"
    when "ask"
      "質問「#{object&.title || 'Unknown'}」を投稿"
    when "answer"
      "質問に回答"
    when "show_question"
      "質問「#{object&.title || 'Unknown'}」を閲覧"
    when "submit_assignment"
      "課題を提出"
    when "mark_assignment"
      "課題が採点されました"
    when "choose_best_answer"
      "ベストアンサーを選択"
    when "show_premium_service"
      "プレミアムサービスを閲覧"
    else
      "#{action}を実行"
    end
  end
end
