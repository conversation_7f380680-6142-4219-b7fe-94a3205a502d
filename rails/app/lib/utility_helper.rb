require "redcarpet"
require "redcarpet/render_strip"
require "nokogiri"

class RougeRenderer < Redcarpet::Render::HTML
  require "rouge"
  require "rouge/plugins/redcarpet"

  include Rouge::Plugins::Redcarpet
end

class UtilityHelper
  def self.get_string_value(object, key, default_value = '')
    return default_value if object.nil? || object[key].nil?

    object[key]
  end

  def self.get_boolean_value(object, key)
    return false if object.blank? || object[key].blank?

    object[key].present?
  end

  def self.get_hash_value(object, key)
    return {} if object.blank? || object[key].blank?

    object[key] || {}
  end

  def self.seconds_to_hms(sec)
    if sec >= 3600
      "%02d:%02d:%02d" % [sec / 3600, sec / 60 % 60, sec % 60]
    else
      "%02d:%02d" % [sec / 60 % 60, sec % 60]
    end
  end

  # Rails7から、なぜかエラーです。
  def self.fix_rails_blob_path(blob_path)
    if Rails.env.development?
      blob_path
    else
      "rails/#{blob_path}"
    end
  end

  def self.ran_dom_str
    (0...8).map { (65 + rand(26)).chr }.join
  end

  def self.truncate_str(str, max_length = 15)
    omission = str.length > max_length ? '...' : ''
    "#{str[0...(max_length - 1)]}#{omission}"
  end

  def self.convert_contact_form_tag body, user_contact_uuid = nil
    # Ensure proper UTF-8 encoding before regex scan
    safe_body = body.to_s.force_encoding('UTF-8')
    safe_body = safe_body.scrub('?') unless safe_body.valid_encoding?

    contact_forms = safe_body.scan(/\[\[(.*?)\]\]/).flatten
    contact_forms.each do |cf|
      contact_form_id = cf.split("-")[-1]
      contact_form = ContactForm.find_by(id: contact_form_id)
      return safe_body unless contact_form
      contact_form_html = ""
      if cf.include?("contact")
        contact_form_html = ParseHtml.convert_to_html(contact_form_id, contact_form.body_tag)
      elsif cf.include?("confirm") && user_contact_uuid
        contact_form_html = ParseHtml.convert_confirm_html user_contact_uuid, contact_form.confirmation_content
      end

      safe_body.gsub!("[[#{cf}]]", contact_form_html&.html_safe || "")
    end
    safe_body.html_safe
  end

  def self.get_title html, subtitle_h3: false
    new_html = ""
    h2_eles = html.split("<h2")
    results = []
    h2_eles.each_with_index do |h, idx|
      h3_list = []
      h = "<h2" + h if idx > 0
      parsed_data = Nokogiri::HTML.fragment(h)
      h2_text = parsed_data.search("h2").text
      if h2_text == ""
         new_html = new_html + parsed_data.to_s
         next
      end
      if subtitle_h3
        h3_eles = parsed_data.search("h3")
        if h3_eles.any?
          h3_eles.each_with_index do |h3, i|
            h3_text = parsed_data.search("h3")[i].text
            h3_ele_id = parsed_data.search("h3")[i].attr("id")
            unless h3_ele_id.present?
              h3_ele_id = "skillhub-subchapter-#{idx}-#{i}"
            end
            h3_list << [h3_ele_id, h3_text]
            parsed_data.search("h3")[i].set_attribute("id", h3_ele_id)
          end
        end
      end
      ele_id = parsed_data.search("h2").first.attr("id")
      unless ele_id.present?
        ele_id = "skillhub-chapter-#{idx}"
      end
      parsed_data.search("h2").first.set_attribute("id", ele_id)
      new_html = new_html + parsed_data.to_s
      results << [ele_id, h2_text, h3_list]
    end
    return new_html, results
  end

  def self.generate_title_html array_title, root_ele: false
    html = ""
    if root_ele
      html = "<ul class='toc_list'>"
    else
      html = "<ul>"
    end
    array_title.each do |at|
      html += "<li><a class='mokuji' href='##{at[0]}'>#{at[1]}</a></li>"
      if at[2].present? && at[2].any?
        html += generate_title_html at[2]
      end
    end
    return html + "</ul>"
  end

  def self.contact_page_preview md
    return '' if md.nil?
    md = convert_contact_form_tag(md)
    markdown = Redcarpet::Markdown.new(
      RougeRenderer,
      autolink: true,
      tables: true,
      fenced_code_blocks: true,
      underline: true,
      strikethrough: true,
      superscript: true,
      highlight: true,
      quote: true,
      footnotes: true,
      escape_html: true,
      hard_wrap: true
    )
    markdown.render(md)
  end

  def self.markdown_to_html md, add_banner: false, request: nil, user: nil, school: nil, subtitle_h2: false, subtitle_h3: false, user_contact_uuid: nil
    return '' if md.nil?
    markdown = Redcarpet::Markdown.new(
      RougeRenderer,
      autolink: true,
      tables: true,
      fenced_code_blocks: true,
      underline: true,
      strikethrough: true,
      superscript: true,
      highlight: true,
      quote: true,
      footnotes: true,
      escape_html: true,
      hard_wrap: true,
      no_styles: false,
      link_attributes: { target: '_blank' }
    )
    markdown = markdown.render(md)
    if add_banner && school
      h2_index = markdown.enum_for(:scan, /(?=<h2)/).map do
        Regexp.last_match.offset(0).first
      end
      obj = ""

      if request
        if request.fullpath.include?("blogs")
          obj = "blog"
        elsif request.fullpath.include?("lessons") || request.fullpath.include?("/api/v2/courses/")
          obj = "lesson"
        end
      end

      if h2_index.size >= 2
        banner = school.banners.published.where("positions like ?", "%#{obj}_before_2th_h2%").first
        if banner && request
          unless user && user.last_subscription_active? && banner.is_not_show_banner_for_premium
            markdown = markdown.insert(h2_index[1], banner.convert_content(request&.base_url, request&.path, user&.id, "#{obj}_before_2th_h2", request&.referrer).html_safe)
          end
        end
      end
      banner_patterns = markdown.scan(/\@\@(.*?)\@\@/).flatten
      banner_patterns.each do |bp|
        banner_id = bp.split("-")[1]
        banner = school.banners.find_by(id: banner_id)
        next unless banner
        if user && user.last_subscription_active? && banner.is_not_show_banner_for_premium
          next
        end
        markdown.gsub!("@@#{bp}@@", banner.convert_content(request.base_url, request.path, user&.id, "inner_#{obj}", request.referrer).html_safe)
      end
    end

    if subtitle_h2
      markdown, array_title = get_title(markdown.html_safe, subtitle_h3: subtitle_h3)
      title_html = generate_title_html array_title, root_ele: true
      parsed_data = Nokogiri::HTML.fragment(markdown)
      h2_eles = parsed_data.search('h2')
      if h2_eles.count >= 2
        parsed_data.search('h2')[0].before(title_html)
      end
      markdown = parsed_data.to_s.html_safe
    end

    # covert highlight
    if markdown.include?('<div class="highlight">')
      markdown = markdown.gsub('<div class="highlight">', %{
      <div class="highlight-container">
        <div class="highlight-header">
          <div class="highlight-lang"></div>
          <div class="highlight-actions">
            <div><button class="flex gap-1 items-center highlight-copy-btn"><span class="v-btn__content"><i class="v-icon material-icons theme&amp;#45;&amp;#45;dark">content_copy</i></span><span class="action-name">コードをコピーする</span></button></div>
            <div><button class="flex gap-1 items-center highlight-zoom-btn"><span class="v-btn__content"><i class="v-icon material-icons theme&amp;#45;&amp;#45;dark">zoom_out_map</i></span></button></div>
          </div>
        </div>
      }).html_safe
    end

    convert_contact_form_tag(markdown, user_contact_uuid).html_safe
  end

  def self.convert_inquiry_form body, request: nil, user: nil, school: nil, csrf_meta_tag: nil
    form_tag="{{ inquiry_form }}"
    return body if body.scan(form_tag).empty? || school.nil?
    token = get_token csrf_meta_tag
    url = request&.path
    form_content = InquiryForm.render user, token, url, school
    body.gsub!(form_tag, form_content)
  end

  def self.get_token csrf_meta_tag
    tag = Nokogiri::XML::DocumentFragment.parse(csrf_meta_tag)
    node = tag.css('meta')
    return node.last.attributes["content"].value
  end

  def self.convert_banner content, request, user, obj
    banner_patterns = content.scan(/\@\@(.*?)\@\@/).flatten
    banner_patterns.each do |bp|
      banner_id = bp.split("-")[1]
      banner = Banner.find_by(id: banner_id)
      next unless banner
      if user && user.last_subscription_active? && banner.is_not_show_banner_for_premium
        next
      end
      content.gsub!("@@#{bp}@@", banner.convert_content(request.base_url, request.path, user&.id, "inner_#{obj}", request.referrer).html_safe)
    end
    return content
  end

  def self.convert_tz_sql time_column
    "DATE(CONVERT_TZ(#{time_column},'+00:00','+9:00'))"
  end

  def self.to_ids arr = []
    arr.map { |target| target.id }
  end

  def self.to_sql_ids arr = []
    "(#{arr.map { |target| target.id }.join(',')})"
  end

  def self.convert_tag_global tag, content, replace_content
    if content.scan(tag).count > 0
      return content.gsub(tag, replace_content).html_safe
    else
      return content
    end
  end

  def self.string_to_bool str
    return true if str == "true"
    false
  end

  def self.get_youtube_id(url)
    id = ''
    url = url.gsub(/(>|<)/i,'').split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/)
    if url[2] != nil
      id = url[2].split(/[^0-9a-z_\-]/i)
      id = id[0];
    else
      id = url.first;
    end
    id
  end

  def self.replace_outside_code_tags(html)
    result = ""
    _result = html.split(/(```.*?```)/m).map(&:strip).reject(&:empty?)
    _result.each do |r|
      if r.include?("```")
        result = result + "\n\n#{r}\n\n"
      else
        r = r.gsub(/<[^>]+>/) do |match|
          "`#{match}`"
        end
        result = result + r
      end
    end
    return result
  end
end
