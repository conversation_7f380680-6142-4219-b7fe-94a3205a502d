module PromptVariables
  extend self

  def initialize_context(object, messages)
    @object = object
    @messages = messages
    @lesson = nil
    @exam = nil
    @question = nil
    @user = messages.last&.user
  end

  def user_name
    @user&.name || 'ユーザー名がありません'
  end

  def user_profile
    return '' unless @user

    profile_parts = []
    profile_parts << "名前: #{@user.name}" if @user.name.present?
    profile_parts << "学年: #{@user.grade}" if @user.respond_to?(:grade) && @user.grade.present?
    profile_parts << "レベル: #{@user.level}" if @user.respond_to?(:level) && @user.level.present?

    profile_parts.join("\n")
  end

  def lesson_title
    ensure_lesson_loaded
    @lesson&.name || 'レッスンタイトルがありません'
  end

  def lesson_body
    ensure_lesson_loaded
    @lesson&.body || 'レッスン本文がありません'
  end

  def lesson_id
    ensure_lesson_loaded
    @lesson&.id&.to_s || 'レッスンIDがありません'
  end

  def materials
    ensure_lesson_loaded
    return 'レッスン教材がありません' unless @lesson&.materials&.present?

    @lesson.materials.map(&:filename).join(', ')
  end

  def exam_title
    ensure_exam_loaded
    @exam&.name || '試験タイトルがありません'
  end

  def exam_description
    ensure_exam_loaded
    @exam&.description || '試験説明がありません'
  end

  def exam_id
    ensure_exam_loaded
    @exam&.id&.to_s || '試験IDがありません'
  end

  def test_title
    ensure_question_loaded
    @question&.exams&.first&.name || '問題タイトルがありません'
  end

  def test_description
    ensure_question_loaded
    @question&.exams&.first&.description || '問題説明がありません'
  end

  def test_id
    ensure_question_loaded
    @question&.exams&.first&.id&.to_s || '問題IDがありません'
  end

  def goal_title
    ensure_goal_loaded
    @goal&.title || '目標タイトルがありません'
  end

  def goal_description
    ensure_goal_loaded
    @goal&.description || '目標説明がありません'
  end

  def goal_id
    ensure_goal_loaded
    @goal&.id&.to_s || '目標IDがありません'
  end

  def progress
    ensure_goal_loaded
    return '目標の進捗がありません' unless @goal

    "進捗: #{@goal.progress_percentage}%"
  end

  def recent_activities
    return '最近のアクティビティがありません' unless @user

    activities = @user.activities.recent.limit(5)
    return '最近のアクティビティがありません' if activities.empty?

    activities.map do |activity|
      "#{activity.created_at.strftime('%Y/%m/%d')}: #{activity.description}"
    end.join("\n")
  end

  def learning_progress
    return '学習進捗がありません' unless @user

    user_goals = @user.user_goals.includes(:goal)
    return '学習進捗がありません' if user_goals.empty?

    progress_info = user_goals.map do |user_goal|
      progress_rate = user_goal.progress_rate
      "目標「#{user_goal.goal.name}」: #{progress_rate}%完了"
    end

    progress_info.join("\n")
  end

  def learning_time_stats
    return '学習時間統計がありません' unless @user

    # 今週の学習時間
    this_week_lessons = @user.enrollment_lessons
                            .joins(:lesson)
                            .where(finished_at: 1.week.ago..Time.current)
                            .count

    # 今月の学習時間
    this_month_lessons = @user.enrollment_lessons
                             .joins(:lesson)
                             .where(finished_at: 1.month.ago..Time.current)
                             .count

    # 今週の試験回数
    this_week_exams = @user.user_exams
                          .where(finished_at: 1.week.ago..Time.current)
                          .count

    stats = []
    stats << "今週完了レッスン: #{this_week_lessons}件"
    stats << "今月完了レッスン: #{this_month_lessons}件"
    stats << "今週受験回数: #{this_week_exams}回"

    stats.join("\n")
  end

  def current_goals
    return '現在の目標がありません' unless @user

    active_goals = @user.user_goals.includes(:goal).where(completed_at: nil)
    return '現在の目標がありません' if active_goals.empty?

    goals_info = active_goals.map do |user_goal|
      progress = user_goal.progress_rate
      days_remaining = user_goal.days_remaining

      goal_text = "目標「#{user_goal.goal.name}」"
      goal_text += " - 進捗: #{progress}%"
      goal_text += " - 残り日数: #{days_remaining}日" if days_remaining > 0
      goal_text
    end

    goals_info.join("\n")
  end

  def learning_streak
    return '学習継続日数がありません' unless @user

    current_date = Date.current
    streak = 0

    # 最大30日まで遡って連続学習日数を計算
    while current_date >= (Date.current - 30.days)
      has_activity = @user.activities
                         .where(created_at: current_date.beginning_of_day..current_date.end_of_day)
                         .where(action: ['enrollment_lesson', 'finish_exam', 'get_exam'])
                         .exists?

      if has_activity
        streak += 1
        current_date -= 1.day
      else
        break
      end
    end

    "連続学習日数: #{streak}日"
  end

  def upcoming_recommendations
    return '推奨学習内容がありません' unless @user

    recommendations = []

    # 未完了のレッスンを取得 - より安全なアプローチ
    begin
      incomplete_enrollments = @user.enrollments
                                   .joins(:course)
                                   .includes(:course)
                                   .limit(3)

      incomplete_enrollments.each do |enrollment|
        recommendations << "コース: #{enrollment.course.name}"
      end
    rescue => e
      # エラーが発生した場合はスキップ
    end

    # 最近の試験結果を取得
    begin
      recent_exams = @user.user_exams
                         .joins(:exam)
                         .where.not(finished_at: nil)
                         .where('finished_at > ?', 1.month.ago)
                         .includes(:exam)
                         .limit(2)

      recent_exams.each do |user_exam|
        if user_exam.average && user_exam.average < 70
          recommendations << "再受験推奨: #{user_exam.exam.name}"
        end
      end
    rescue => e
      # エラーが発生した場合はスキップ
    end

    return '推奨学習内容がありません' if recommendations.empty?
    recommendations.join("\n")
  end

  private

  def ensure_lesson_loaded
    return if @lesson.present? || @object.targetable_type != "Lesson"

    @lesson = Lesson.find(@object.targetable_id)
  end

  def ensure_exam_loaded
    return if @exam.present? || @object.targetable_type != "Exam"

    @exam = Exam.find(@object.targetable_id)
  end

  def ensure_question_loaded
    return if @question.present? || @object.targetable_type != "Question"

    @question = Q.find(@object.targetable_id)
  end

  def ensure_goal_loaded
    return if @goal.present? || @object.targetable_type != "Goal"

    @goal = Goal.find(@object.targetable_id)
  end
end
