require 'ostruct'

module PlaygroundChat
  extend self

  def chat_stream(messages, object, process_id, session_id, system_message_content: nil, lesson_object: nil, session: nil, agent_id: nil)
    channel = "playground_chat_#{object.to_user_id}_#{session_id}"

    Rails.logger.info "PlaygroundChat streaming to channel: #{channel}"
    Rails.cache.write(process_id, true)

    request_custom_ai(messages, object, system_message_content: system_message_content, lesson_object: lesson_object, channel: channel, session: session, agent_id: agent_id) do |content, reasoning_content, chunk|
      if chunk&.content.present? || chunk&.reasoning_content.present?
        is_generating = Rails.cache.read(process_id)
        if is_generating
          ActionCable.server.broadcast channel, {
            message_id: object.id,
            content: ApplicationController.helpers.emojify(
              UtilityHelper.markdown_to_html(
                UtilityHelper.replace_outside_code_tags(content)
              )
            ).html_safe,
            reasoning_content: reasoning_content,
            finished: false
          }
        else
          if object.class.name == "Message"
            object.update(content: content, reasoning_content: reasoning_content)
          else
            object.update(body: content)
          end
          break
        end
      end
    end

    is_generating = Rails.cache.read(process_id)

    ActionCable.server.broadcast channel, {
      message_id: object.id,
      content: "Finished",
      finished: true
    }

    is_generating
  end

  def request_custom_ai(messages, object = nil, system_message_content: nil, lesson_object: nil, channel: nil, session: nil, agent_id: nil)
    @current_session = session

    @debug_user_id = Thread.current[:current_user]&.id
    Thread.current[:debug_user_id] = @debug_user_id

    unless is_supported_object?(object)
      error_chunk = OpenStruct.new(content: "エラー: Lesson、Exam、またはSchoolオブジェクトのみサポートされています。", reasoning_content: "エラー: Lesson、Exam、またはSchoolオブジェクトのみサポートされています。")
      yield "エラー: Lesson、Exam、またはSchoolオブジェクトのみサポートされています。", "エラー: Lesson、Exam、またはSchoolオブジェクトのみサポートされています。", error_chunk
      return
    end

    school = get_school_from_object(object)

    ai_platform_school = school.ai_platform_school

    if ai_platform_school.blank? || ai_platform_school.api_key.blank? || ai_platform_school.model.blank?
      error_chunk = OpenStruct.new(content: "AI model not configured yet.", reasoning_content: "AI model not configured yet.")
      yield "AI model not configured yet.", "AI model not configured yet.", error_chunk
      return
    end

    config = RubyLLM::Configuration.new
    config.anthropic_api_key = ai_platform_school.api_key
    config.openai_api_key = ai_platform_school.api_key
    config.gemini_api_key = ai_platform_school.api_key
    config.deepseek_api_key = ai_platform_school.api_key
    model = ai_platform_school.model

    chat = RubyLLM.chat(model: model, context: RubyLLM::Context.new(config))

    user_id = object&.to_user_id
    if user_id
      current_user = User.find(user_id) rescue nil
      Thread.current[:current_user] = current_user
    end

    # Determine context type for agent selection
    is_dashboard_context = object.is_a?(School) || (object.respond_to?(:targetable_type) && object.targetable_type == "School")
    lesson = lesson_object || (is_dashboard_context ? nil : get_lesson_from_object(object))
    selected_agent = get_selected_agent(school, agent_id, lesson, is_dashboard_context)

    # Debug logging for agent selection
    DebugLogger.add_debug_info(nil, 'agent_selection', "Agent selected for context", {
      is_dashboard_context: is_dashboard_context,
      agent_id: agent_id,
      selected_agent_id: selected_agent&.id,
      selected_agent_type: selected_agent&.agent_type,
      selected_agent_name: selected_agent&.name,
      object_type: object.class.name,
      targetable_type: object.respond_to?(:targetable_type) ? object.targetable_type : nil
    }, nil, selected_agent&.id)

    add_function_calling_tools(chat, school, selected_agent)

    if system_message_content.present?
      system_message = RubyLLM::Message.new(
        role: :system,
        content: system_message_content
      )
      chat.add_message(system_message)
    end

    last_student_message = messages.select { |m| m.owner_type == 'student' }.last

    # Add context based on object type
    if object.is_a?(School) || (object.respond_to?(:targetable_type) && object.targetable_type == "School")
      add_dashboard_context(chat, object, last_student_message&.content, school, selected_agent)
    else
      add_lesson_context(chat, object, last_student_message&.content, school, selected_agent)
    end

    messages.each do |message|
      attachments = school.using_deepseek? ? [] : message.attachments.map(&:url)

      chat_message = RubyLLM::Message.new(
        role: message.bot? ? :assistant : :user,
        content: RubyLLM::Content.new(
          message.content,
          attachments
        )
      )

      chat.add_message(chat_message)
    end

    content = ""
    reasoning_content = ""
    if (msg = messages.where(owner_type: "bot").last) && (prompt = chat.messages.select { |m| m.role == :system }.map(&:content).join("\n\n")).present?
      begin
        cleaned_prompt = prompt.gsub(/<ctx>(.*?)<\/ctx>/m, '')

        truncated_prompt = cleaned_prompt.length > 1_000_000 ?
          cleaned_prompt[0..999_000] + "\n\n[TRUNCATED - Original length: #{cleaned_prompt.length} chars]" :
          cleaned_prompt

        msg.update(system_prompt: truncated_prompt)
        Rails.logger.info "Saved system prompt (#{truncated_prompt.length} chars) to message #{msg.id}"
        user_input = messages.where(owner_type: "student").last&.content || "No user input"
        DebugLogger.add_debug_info(nil, 'user_input', "User Input", user_input, nil, selected_agent&.id)
        DebugLogger.add_debug_info(nil, 'system_prompt', "System prompt generated", {
          message_id: msg.id,
          prompt_length: truncated_prompt.length,
          was_truncated: cleaned_prompt.length > 1_000_000,
          original_length: cleaned_prompt.length,
          system_prompt: truncated_prompt
        }, nil, selected_agent&.id)

      rescue => e
        Rails.logger.error "Failed to save system prompt to message #{msg.id}: #{e.message}"

        DebugLogger.add_debug_info(nil, 'error', "System prompt save failed", {
          message_id: msg&.id,
          error_message: e.message,
          error_class: e.class.name
        }, nil, selected_agent&.id)
      end
    end

    chat.complete do |chunk|
      if chunk.content
        content += chunk.content
        yield content, reasoning_content, chunk
      end

      if chunk.reasoning_content
        reasoning_content += chunk.reasoning_content
        yield content, reasoning_content, chunk
      end
    end
  rescue => e
    Rails.logger.error "Error in PlaygroundChat.request_custom_ai: #{e.message}"
    puts "Error in PlaygroundChat.request_custom_ai: #{e.message}"
    error_chunk = OpenStruct.new(content: "エラーが発生しました。", reasoning_content: "エラーが発生しました。")
    yield "エラーが発生しました。", "エラーが発生しました。", error_chunk
  ensure
    Thread.current[:current_user] = nil

    return unless object
    return "" if content.blank?

    attachments = charts_from_content(content)
    if attachments.present?
      md = attachments.map { |a| "![draw](#{a.url})" }.join("\n")
      attachment_chunk = OpenStruct.new(content: content + "\n" + md, reasoning_content: md)
      yield content + "\n" + md, md, attachment_chunk
    end

    if attachments.present?
      if object.class.name == "Message"
        object.update(content: content, reasoning_content: reasoning_content, attachment_ids: attachments.map(&:id))
      else
        object.update(body: content, attachment_ids: attachments.map(&:id))
      end
    else
      if object.class.name == "Message"
        object.update(content: content, reasoning_content: reasoning_content)
      else
        object.update(body: content)
      end
    end

    content
  end

  private

  def is_supported_object?(object)
    object && (object.is_a?(Lesson) || object.is_a?(Exam) || object.is_a?(School) ||
               (object.respond_to?(:targetable_type) && ["Lesson", "School"].include?(object.targetable_type)))
  end

  def get_school_from_object(object)
    if object.is_a?(Lesson)
      object.school
    elsif object.is_a?(Exam)
      object.school
    elsif object.is_a?(School)
      object.school
    elsif object.respond_to?(:targetable_type)
      case object.targetable_type
      when "Lesson"
        lesson = Lesson.find(object.targetable_id)
        lesson.school
      when "Exam"
        exam = Exam.find(object.targetable_id)
        exam.school
      when "School"
        school = School.find(object.targetable_id)
        school
      else
        raise "School not found for targetable type: #{object.targetable_type}"
      end
    else
      raise "Unsupported object type: #{object.class.name}"
    end
  end

  def get_lesson_from_object(object)
    if object.is_a?(Lesson)
      object
    elsif object.respond_to?(:targetable_type) && object.targetable_type == "Lesson"
      Lesson.find(object.targetable_id)
    else
      raise "Unsupported object type: #{object.class.name}"
    end
  end

  def add_lesson_context(chat, object_or_lesson, user_input, school, selected_agent = nil)
    lesson = object_or_lesson.is_a?(Lesson) ? object_or_lesson : get_lesson_from_object(object_or_lesson)
    return unless lesson

    lesson_context = get_lesson_rag_context(user_input, lesson, school)

    agent_for_context = selected_agent || school.ai_tutor_agents.find_by(agent_type: 'lesson')
    agent_context = get_agent_rag_context(user_input, agent_for_context, school)
    combined_context = [lesson_context, agent_context].reject(&:blank?).join("\n\n---\n\n")

    if combined_context.present?
      if lesson_context.present?
        DebugLogger.add_debug_info(nil, 'lesson_context', "Lesson context", {
          lesson_id: lesson.id,
          lesson_name: lesson.name,
          lesson_context: lesson_context
        }, nil, selected_agent&.id)
      end

      if agent_context.present? && agent_for_context
        DebugLogger.add_debug_info(nil, 'agent_context', "Agent context", {
          agent_id: agent_for_context.id,
          agent_name: agent_for_context.name,
          agent_context: agent_context
        }, nil, selected_agent&.id)
      end

      lesson_context_message = RubyLLM::Message.new(
        role: :system,
        content: "あなたはAIアシスタントです。生徒の質問に答えてください。画像に関する質問がある場合は、提供された画像の説明を参照してください。Pineconeのコンテキスト情報は補足的に使用し、質問に直接関係ない場合は無視してください。常に簡潔で的確な回答を心がけてください。\n
  Pineconeコンテキスト: #{combined_context}"
      )

      chat.add_message(lesson_context_message)
    end
  end

  def add_dashboard_context(chat, object, user_input, school, selected_agent = nil)
    target_school = object.is_a?(School) ? object : school
    return unless target_school

    agent_for_context = selected_agent || school.ai_tutor_agents.find_by(agent_type: 'dashboard')
    agent_context = get_agent_rag_context(user_input, agent_for_context, school) if agent_for_context

    dashboard_context_parts = []

    if agent_context.present?
      dashboard_context_parts << "エージェント情報:\n#{agent_context}"
    end

    # Add user-specific dashboard context
    user = Thread.current[:current_user]
    if user
      dashboard_context_parts << "ユーザー情報:\n名前: #{user.name}"

      # Add recent learning activities
      if user.activities.respond_to?(:recent)
        recent_activities = user.activities.recent.limit(5)
        if recent_activities.any?
          activities_text = recent_activities.map { |a| "#{a.created_at.strftime('%Y/%m/%d')}: #{a.action}" }.join("\n")
          dashboard_context_parts << "最近の学習活動:\n#{activities_text}"
        end
      end
    end

    combined_context = dashboard_context_parts.reject(&:blank?).join("\n\n---\n\n")

    if combined_context.present?
      if agent_context.present? && agent_for_context
        DebugLogger.add_debug_info(nil, 'dashboard_agent_context', "Dashboard agent context", {
          agent_id: agent_for_context.id,
          agent_name: agent_for_context.name,
          agent_context: agent_context
        }, nil, selected_agent&.id)
      end

      dashboard_context_message = RubyLLM::Message.new(
        role: :system,
        content: "あなたはダッシュボード専用のAIアシスタントです。学習状況を分析し、今日何を勉強すべきか具体的にアドバイスしてください。画像に関する質問がある場合は、提供された画像の説明を参照してください。Pineconeのコンテキスト情報は補足的に使用し、質問に直接関係ない場合は無視してください。常に簡潔で的確な回答を心がけてください。\n
ダッシュボードコンテキスト: #{combined_context}"
      )
      chat.add_message(dashboard_context_message)
    end
  end

  def get_lesson_rag_context(query, lesson, school)
    return "" if query.blank?

    begin
      lesson_agent = school.ai_tutor_agents.find_by(agent_type: 'lesson')
      return "" unless lesson_agent

      rag_config = lesson_agent.ai_tutor_rag_configs.first
      return "" unless rag_config&.enabled?

      search_params = rag_config.search_params_for({
        lesson_id: lesson.id,
      })

      result = AiTutorRagService.search_materials(
        query: query,
        school: school,
        **search_params
      )

      if result[:success] && result[:results].present?
        contexts = result[:results].map do |item|
          item[:text] || item['text']
        end.compact

        return contexts.join("\n\n")
      else
        Rails.logger.info "No lesson RAG context found for query: #{query}"
        return ""
      end
    rescue => e
      Rails.logger.error "Error getting lesson RAG context in playground: #{e.message}"
      return ""
    end
  end

  def get_agent_rag_context(query, agent, school)
    return "" if query.blank?

    begin
      rag_config = agent.ai_tutor_rag_configs.first
      return "" unless rag_config&.enabled?

      search_params = rag_config.search_params_for({})

      result = AiTutorRagService.search_materials(
        query: query,
        school: school,
        **search_params
      )

      if result[:success] && result[:results].present?
        contexts = result[:results].map do |item|
          item[:text] || item['text']
        end.compact

        return contexts.join("\n\n")
      else
        Rails.logger.info "No agent RAG context found for query: #{query}"
        return ""
      end
    rescue => e
      Rails.logger.error "Error getting agent RAG context in playground: #{e.message}"
      return ""
    end
  end

  def add_function_calling_tools(chat, school, selected_agent)
    begin
      agent = selected_agent
      unless agent
        DebugLogger.add_debug_info(nil, 'function_calling', "No agent available for function calling", {
          school_id: school.id,
          selected_agent: selected_agent
        })
        return
      end

      tools_count = agent.ai_tutor_tools.enabled.count
      DebugLogger.add_debug_info(nil, 'function_calling', "Adding function calling tools", {
        agent_id: agent.id,
        agent_type: agent.agent_type,
        agent_name: agent.name,
        tools_count: tools_count
      }, nil, agent.id)

      agent.ai_tutor_tools.enabled.each_with_index do |tool, index|
        begin
          dynamic_tool_class = create_dynamic_tool_class(tool)
        rescue => e
          Rails.logger.error "Error creating tool class for #{tool.tool_name}: #{e.message}"
          Rails.logger.error "Error backtrace: #{e.backtrace.first(3).join("\n")}"

          DebugLogger.add_debug_info(nil, 'error', "Failed to create tool: #{tool.tool_name}", {
            tool_name: tool.tool_name,
            error_message: e.message,
            error_class: e.class.name,
            backtrace: e.backtrace.first(3)
          }, nil, agent.id)

          dynamic_tool_class = nil
        end

        if dynamic_tool_class
          DebugLogger.add_debug_info(nil, 'tool_added', "Tool added: #{tool.tool_name}", {
            tool_name: tool.tool_name,
            tool_type: tool.tool_type,
            description: tool.description,
            when_to_use: tool.when_to_use
          })

          chat.with_tool(dynamic_tool_class)
        else
          DebugLogger.add_debug_info(nil, 'error', "Tool creation failed: #{tool.tool_name}", {
            tool_name: tool.tool_name,
            error_message: "Dynamic tool class creation returned nil",
            possible_causes: [
              "Function definition parsing failed",
              "Method signature not recognized",
              "Invalid function syntax"
            ]
          })
        end
      end
    rescue => e
      Rails.logger.error "Error in add_function_calling_tools: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e
    end
  end

  def create_dynamic_tool_class(custom_tool)
    return nil if custom_tool.function_definition.blank?

    method_info = parse_function_definition(custom_tool.function_definition)
    unless method_info
      Rails.logger.error "Failed to parse function definition for #{custom_tool.tool_name}"

      DebugLogger.add_debug_info(nil, 'error', "Function parsing failed: #{custom_tool.tool_name}", {
        tool_name: custom_tool.tool_name,
        error_message: "Could not parse function definition",
        function_definition_preview: custom_tool.function_definition[0..200] + (custom_tool.function_definition.length > 200 ? "..." : ""),
        expected_format: "def method_name or def method_name(params)"
      })

      return nil
    end

    tool_class = Class.new(RubyLLM::Tool) do
      full_description = custom_tool.description
      if custom_tool.when_to_use.present?
        full_description += "\n When to use: #{custom_tool.when_to_use}"
      end

      DebugLogger.add_debug_info(nil, 'tool_created', "Tool class created: #{custom_tool.tool_name}", {
        full_description: full_description,
        method_name: method_info[:method_name],
        params: method_info[:params]
      })

      description full_description

      method_info[:params].each do |param_name, param_info|
        next if param_name == 'user_id'
        param param_name.to_sym,
              desc: param_info[:description] || param_name.to_s,
              required: param_info[:required] != false
      end

      @function_definition = custom_tool.function_definition
      @method_name = method_info[:method_name]

      define_method :execute do |**params|
        begin
          execution_context = create_execution_context

          execution_context.instance_eval(self.class.instance_variable_get(:@function_definition))

          method_name = self.class.instance_variable_get(:@method_name)
          if execution_context.respond_to?(method_name, true)
            result = execution_context.send(method_name, **params)

            DebugLogger.add_debug_info(nil, 'tool_result', "Success: #{custom_tool.tool_name}", {
              result: result
            })

            result
          else
            error_msg = "Method #{method_name} not found in function definition"
            { error: error_msg }
          end
        rescue => e
          DebugLogger.add_debug_info(nil, 'error', "Error in #{custom_tool.tool_name}: #{e.message}", {
            error_class: e.class.name,
            backtrace: e.backtrace.first(3)
          })

          { error: "Function execution failed: #{e.message}" }
        end
      end

      private

      define_method :create_execution_context do
        context = Object.new

        context.define_singleton_method(:User) { ::User }
        context.define_singleton_method(:School) { ::School }
        context.define_singleton_method(:Lesson) { ::Lesson }
        context.define_singleton_method(:Exam) { ::Exam }
        context.define_singleton_method(:Rails) { ::Rails }

        context.define_singleton_method(:current_user) { Thread.current[:current_user] }

        context.define_singleton_method(:days) { |n| n.days }
        context.define_singleton_method(:weeks) { |n| n.weeks }
        context.define_singleton_method(:months) { |n| n.months }
        context.define_singleton_method(:ago) { |duration| duration.ago }

        context
      end
    end

    tool_class.define_singleton_method(:name) { "CustomTool_#{custom_tool.tool_name}" }

    tool_class
  rescue => e
    Rails.logger.error "Failed to create dynamic tool class for #{custom_tool.tool_name}: #{e.message}"
    nil
  end

  def parse_function_definition(code)
    method_match = code.match(/def\s+(\w+)(?:\s*\(([^)]*)\))?/)
    return nil unless method_match

    method_name = method_match[1]
    params_string = method_match[2] || ""

    params = {}
    if params_string.present?
      params_string.split(',').each do |param|
        param = param.strip
        if param.include?(':')
          parts = param.split(':')
          param_name = parts[0].strip
          default_value = parts[1]&.strip
          params[param_name] = {
            description: param_name.humanize,
            required: !default_value || default_value == 'nil'
          }
        else
          param_name = param.gsub(/[^a-zA-Z0-9_]/, '')
          params[param_name] = {
            description: param_name.humanize,
            required: true
          }
        end
      end
    end

    {
      method_name: method_name,
      params: params
    }
  rescue => e
    Rails.logger.error "Failed to parse function definition: #{e.message}"
    nil
  end

  def charts_from_content(content)
    content.split("```").select do |child|
      text = child.strip
      text.starts_with?("python") && text.ends_with?("show()") && text.include?("import matplotlib.pyplot")
    end.map do |child|
      nil
    end.compact
  end

  # Get the selected AI tutor agent
  def get_selected_agent(school, agent_id, lesson = nil, is_dashboard_context = false)
    if agent_id.present?
      agent = school.ai_tutor_agents.enabled.find_by(id: agent_id)
      return agent if agent
    end

    # Select agent based on context
    if is_dashboard_context
      school.ai_tutor_agents.default_agents.enabled.first ||
      school.ai_tutor_agents.enabled.find_by(agent_type: 'dashboard') ||
      school.ai_tutor_agents.enabled.first
    else
      school.ai_tutor_agents.default_agents.enabled.first ||
      school.ai_tutor_agents.enabled.find_by(agent_type: 'lesson') ||
      school.ai_tutor_agents.enabled.first
    end
  end
end
