include ActionView::Helpers::SanitizeHelper
include ActionView::Helpers::TextHelper

module DashboardChat
  extend self

  def chat_steam(messages, object, process_id, system_message_content: nil, agent_id: nil)
    unless object.targetable_type == "School"
      Rails.logger.error "DashboardChat only supports School objects, got: #{object.targetable_type}"
      return
    end

    Rails.cache.write(process_id, true)
    channel = "user:#{object.to_user_id}:#{object.targetable_type}:#{object.targetable_id}:chats"

    final_content = ""
    final_reasoning_content = ""

    DashboardChat.request_custom_ai(messages, object, system_message_content: system_message_content, agent_id: agent_id) do |content, reasoning_content, chunk|
      final_content = content
      final_reasoning_content = reasoning_content

      if chunk.content.present? || chunk.reasoning_content.present?
        is_generating = Rails.cache.read(process_id)

        if is_generating
          ActionCable.server.broadcast channel, {
            message_id: object.id,
            content: ApplicationController.helpers.emojify(UtilityHelper.markdown_to_html UtilityHelper.replace_outside_code_tags(content)).html_safe,
            reasoning_content: reasoning_content,
            finished: false
          }
        else
          break
        end
      end
    end

    is_generating = Rails.cache.read(process_id)

    # Update final content to database
    if final_content.present?
      object.update(content: final_content, reasoning_content: final_reasoning_content)
    else
      Rails.logger.error "No final content to update!"
    end

    ActionCable.server.broadcast channel, {
      message_id: object.id,
      content: "Finished",
      finished: true
    }

    is_generating
  end

  def request_custom_ai(messages, object = nil, system_message_content: nil, agent_id: nil)
    # Only support School objects
    unless object&.targetable_type == "School"
      Rails.logger.error "Error: Only School objects supported, got: #{object&.targetable_type}"
      yield "エラー: Schoolオブジェクトのみサポートされています。", "エラー: Schoolオブジェクトのみサポートされています。", OpenStruct.new(content: "エラー", reasoning_content: "")
      return
    end

    school = School.find(object.targetable_id)

    ai_platform_school = school.ai_platform_school

    if ai_platform_school.blank? || ai_platform_school.api_key.blank? || ai_platform_school.model.blank?
      Rails.logger.error "AI configuration missing - Platform: #{ai_platform_school.present?}, API Key: #{ai_platform_school&.api_key.present?}, Model: #{ai_platform_school&.model.present?}"
      yield "AI model not configured yet.", "AI model not configured yet.", OpenStruct.new(content: "AI model not configured yet.", reasoning_content: "")
      return
    end

    config = RubyLLM::Configuration.new
    config.anthropic_api_key = ai_platform_school.api_key
    config.openai_api_key = ai_platform_school.api_key
    config.gemini_api_key = ai_platform_school.api_key
    config.deepseek_api_key = ai_platform_school.api_key
    model = ai_platform_school.model

    chat = RubyLLM.chat(model: model, context: RubyLLM::Context.new(config))

    # Set current user in thread for function calling tools
    user_id = object&.to_user_id
    if user_id
      current_user = User.find(user_id) rescue nil
      Thread.current[:current_user] = current_user
    end

    selected_agent = get_selected_agent(school, agent_id)

    add_function_calling_tools(chat, school, selected_agent&.agent_category || 'dashboard')

    if system_message_content.present?
      system_message = RubyLLM::Message.new(
        role: :system,
        content: system_message_content
      )
      chat.add_message(system_message)
    end

    last_student_message = messages.select { |m| m.owner_type == 'student' }.last
    add_dashboard_context(chat, school, last_student_message&.content, selected_agent)

    messages.each_with_index do |message, index|
      attachments = school.using_deepseek? ? [] : message.attachments.map(&:url)
      role = message.bot? ? :assistant : :user

      chat_message = RubyLLM::Message.new(
        role: role,
        content: RubyLLM::Content.new(
          message.content,
          attachments
        )
      )

      chat.add_message(chat_message)
    end

    content = ""
    reasoning_content = ""
    chunk_count = 0

    chat.complete do |chunk|
      chunk_count += 1

      if chunk.content
        content += chunk.content
        yield content, reasoning_content, chunk
      end

      if chunk.reasoning_content
        reasoning_content += chunk.reasoning_content
        yield content, reasoning_content, chunk
      end

      # Also yield if no content but chunk exists (for progress tracking)
      if chunk.content.blank? && chunk.reasoning_content.blank?
        yield content, reasoning_content, chunk
      end
    end

  rescue => e
    Rails.logger.error "Error in DashboardChat.request_custom_ai: #{e.message}"
    Rails.logger.error "Backtrace: #{e.backtrace.first(5).join('\n')}"
    yield "エラーが発生しました。", "エラーが発生しました。", OpenStruct.new(content: "エラーが発生しました。", reasoning_content: "")
  ensure
    Thread.current[:current_user] = nil

    return unless object
    return "" if content.blank?
  end

  private

  # Get the selected AI tutor agent for dashboard
  def get_selected_agent(school, agent_id)
    if agent_id.present?
      agent = school.ai_tutor_agents.enabled.find_by(id: agent_id)
      return agent if agent
    end

    school.ai_tutor_agents.default_agents.enabled.find_by(agent_type: 'dashboard') ||
    school.ai_tutor_agents.enabled.find_by(agent_type: 'dashboard') ||
    school.ai_tutor_agents.enabled.first
  end

  def add_dashboard_context(chat, school, user_input, selected_agent = nil)
    agent_for_context = selected_agent || school.ai_tutor_agents.find_by(agent_type: 'dashboard')
    if agent_for_context
      agent_context = get_agent_rag_context(user_input, agent_for_context, school)
      if agent_context.present?
        dashboard_context_message = RubyLLM::Message.new(
          role: :system,
          content: "Dashboard Context: #{agent_context}"
        )
        chat.add_message(dashboard_context_message)
      end
    end
  end

  # Get agent-specific RAG context
  def get_agent_rag_context(query, agent, school)
    return "" unless query.present? && agent.present?

    begin
      rag_config = agent.ai_tutor_rag_configs.first
      return "" unless rag_config&.enabled?

      search_params = rag_config.search_params_for({})

      result = AiTutorRagService.search_materials(
        query: query,
        school: school,
        **search_params
      )

      if result[:success] && result[:results].present?
        contexts = result[:results].map do |item|
          item[:text] || item['text']
        end.compact

        return contexts.join("\n\n")
      else
        return ""
      end
    rescue => e
      return ""
    end
  end

  # Add function calling tools for dashboard
  def add_function_calling_tools(chat, school, agent_category)
    # Add dashboard-specific tools here
  end
end
