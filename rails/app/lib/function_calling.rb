module FunctionCalling
  extend self

  def default_tools(school: nil)
    base_tools = [
      {
        tool_name: 'search_learning_history',
        tool_type: 'search',
        description: 'Searches the student\'s learning history (lessons and courses).',
        when_to_use: 'Use when a student wants to review their past learning history or check their learning status for a specific period or subject.',
        function_definition: search_learning_history_definition,
        enabled: true,
        is_default: true
      },
      {
        tool_name: 'get_user_weaknesses',
        tool_type: 'analysis',
        description: 'Analyzes student weaknesses based on performance data.',
        when_to_use: 'Use when you want to identify a student\'s weak areas, find points for improvement, or determine a direction for individualized instruction.',
        function_definition: get_user_weaknesses_definition,
        enabled: true,
        is_default: true
      },
      {
        tool_name: 'check_learning_progress',
        tool_type: 'progress',
        description: 'Comprehensively checks learning progress and statistics.',
        when_to_use: 'Use when you want to comprehensively evaluate a student\'s learning progress, check learning statistics or achievement rates, or review a learning plan.',
        function_definition: check_learning_progress_definition,
        enabled: true,
        is_default: true
      },
      {
        tool_name: 'get_daily_study_plan',
        tool_type: 'planning',
        description: 'Creates a personalized daily study plan based on user\'s current progress and goals.',
        when_to_use: 'Use when a student asks what to study today, needs a study schedule, or wants recommendations for daily learning activities.',
        function_definition: get_daily_study_plan_definition,
        enabled: true,
        is_default: true
      },
      {
        tool_name: 'analyze_learning_patterns',
        tool_type: 'analytics',
        description: 'Analyzes learning patterns, habits, and provides insights for improvement.',
        when_to_use: 'Use when a student wants to understand their learning habits, identify peak study times, or get insights about their learning patterns.',
        function_definition: analyze_learning_patterns_definition,
        enabled: true,
        is_default: true
      },
      {
        tool_name: 'get_motivation_boost',
        tool_type: 'motivation',
        description: 'Provides personalized motivation and encouragement based on recent achievements.',
        when_to_use: 'Use when a student seems demotivated, asks for encouragement, or needs motivation to continue studying.',
        function_definition: get_motivation_boost_definition,
        enabled: true,
        is_default: true
      }
    ]

    return base_tools unless school

    base_tools.map do |tool|
      custom_tool = find_custom_tool_setting(school, tool[:tool_name])
      if custom_tool&.when_to_use.present?
        tool.merge(when_to_use: custom_tool.when_to_use)
      else
        tool
      end
    end
  end

  def dashboard_tools
    [
      {
        tool_name: 'get_daily_study_plan',
        tool_type: 'planning',
        description: 'Creates a personalized daily study plan based on user\'s current progress and goals.',
        when_to_use: 'Use when a student asks what to study today, needs a study schedule, or wants recommendations for daily learning activities.',
        function_definition: get_daily_study_plan_definition,
        enabled: true,
        is_default: true
      },
      {
        tool_name: 'analyze_learning_patterns',
        tool_type: 'analytics',
        description: 'Analyzes learning progress, completion rates, and provides insights.',
        when_to_use: 'Use when a student wants to understand their learning progress, check completion rates, or get performance insights.',
        function_definition: analyze_learning_progress_definition,
        enabled: true,
        is_default: true
      },
      {
        tool_name: 'get_motivation_boost',
        tool_type: 'motivation',
        description: 'Provides motivational messages based on recent achievements and progress.',
        when_to_use: 'Use when a student needs encouragement, motivation, or wants to celebrate achievements.',
        function_definition: get_motivation_message_definition,
        enabled: true,
        is_default: true
      }
    ]
  end

  def lesson_tools
    [
      {
        tool_name: 'search_learning_history',
        tool_type: 'search',
        description: 'Searches the student\'s learning history (lessons and courses).',
        when_to_use: 'Use when a student wants to review their past learning history or check their learning status for a specific period or subject.',
        function_definition: search_learning_history_definition,
        enabled: true,
        is_default: true
      },
      {
        tool_name: 'get_user_weaknesses',
        tool_type: 'analysis',
        description: 'Analyzes student weaknesses based on performance data.',
        when_to_use: 'Use when you want to identify a student\'s weak areas, find points for improvement, or determine a direction for individualized instruction.',
        function_definition: get_user_weaknesses_definition,
        enabled: true,
        is_default: true
      },
      {
        tool_name: 'check_learning_progress',
        tool_type: 'progress',
        description: 'Comprehensively checks learning progress and statistics.',
        when_to_use: 'Use when you want to comprehensively evaluate a student\'s learning progress, check learning statistics or achievement rates, or review a learning plan.',
        function_definition: check_learning_progress_definition,
        enabled: true,
        is_default: true
      },
    ]
  end

  private

  def find_custom_tool_setting(school, tool_name)
    school.ai_tutor_tools.find_by(tool_name: tool_name)
  end

  def search_learning_history_definition
    <<~RUBY
      def search_learning_history(days: 30)
        user = Thread.current[:current_user] || raise("ユーザー情報が取得できません")
        start_date = days.to_i.zero? ? 30.day.ago : days.to_i.days.ago

        enrollment_lessons = user.enrollment_lessons
                                .joins(:lesson)
                                .where("enrollment_lessons.created_at >= ?", start_date)
                                .includes(:lesson, :enrollment)
                                .order(created_at: :desc)
                                .limit(50)

        enrollments = user.enrollments
                         .joins(:course)
                         .where("enrollments.created_at >= ?", start_date)
                         .includes(:course)
                         .order(created_at: :desc)
                         .limit(20)

        results = []

        enrollment_lessons.each do |enrollment_lesson|
          results << {
            type: 'lesson',
            date: enrollment_lesson.created_at.strftime("%Y-%m-%d %H:%M"),
            title: enrollment_lesson.lesson.name,
            status: enrollment_lesson.finished_at ? '完了' : '進行中',
            count: enrollment_lesson.count || 0,
            finished_at: enrollment_lesson.finished_at&.strftime("%Y-%m-%d %H:%M"),
            course: enrollment_lesson.enrollment.course.name
          }
        end

        enrollments.each do |enrollment|
          results << {
            type: 'course',
            date: enrollment.created_at.strftime("%Y-%m-%d %H:%M"),
            title: enrollment.course.name,
            status: enrollment.finished? ? '完了' : '受講中',
            lessons_count: enrollment.enrollment_lessons_count || 0,
            progress_rate: enrollment.progress_rate || 0
          }
        end

        results.sort_by { |r| r[:date] }.reverse
      rescue => e
        { error: "学習履歴の取得に失敗しました: \#{e.message}" }
      end
    RUBY
  end

  def get_user_weaknesses_definition
    <<~RUBY
      def get_user_weaknesses
        user = Thread.current[:current_user] || raise("ユーザー情報が取得できません")

        # Analyze lesson performance
        weak_lessons = user.enrollment_lessons
                          .joins(:lesson)
                          .where("finished_at IS NULL OR count < ?", 3)
                          .includes(:lesson, :enrollment)
                          .limit(10)

        lesson_weaknesses = weak_lessons.map do |enrollment_lesson|
          count = enrollment_lesson.count || 0
          days_since_start = (Time.current - enrollment_lesson.created_at) / 1.day

          difficulty_level = if count == 0 && days_since_start > 7
                              'とても難しい'
                            elsif count < 2 && days_since_start > 3
                              '難しい'
                            elsif count < 5
                              '普通'
                            else
                              '簡単'
                            end

          {
            lesson_name: enrollment_lesson.lesson.name,
            count: count,
            status: enrollment_lesson.finished_at ? 'ゆっくり完了' : '未完了',
            course: enrollment_lesson.enrollment.course.name,
            difficulty_level: difficulty_level,
            created_at: enrollment_lesson.created_at.strftime("%Y-%m-%d")
          }
        end

        # Analyze course performance
        weak_courses = user.enrollments
                          .joins(:course)
                          .where("enrollment_lessons_count < ?", 5)
                          .includes(:course)
                          .limit(5)

        course_weaknesses = weak_courses.select { |e| !e.finished? }.map do |enrollment|
          {
            course_name: enrollment.course.name,
            lessons_completed: enrollment.enrollment_lessons_count || 0,
            progress_rate: enrollment.progress_rate || 0,
            status: enrollment.finished? ? '完了' : '進行中',
            recommendation: "このコースのレッスンをもっと完了しましょう",
            enrolled_at: enrollment.created_at.strftime("%Y-%m-%d")
          }
        end

        overall_recommendation = if lesson_weaknesses.any? || course_weaknesses.any?
                                  "未完了のレッスンや進捗の遅いコースを復習しましょう"
                                else
                                  "全体的に良い進捗です！この調子で学習を続けましょう"
                                end

        {
          lesson_weaknesses: lesson_weaknesses,
          course_weaknesses: course_weaknesses,
          overall_recommendation: overall_recommendation,
          analysis_date: Time.current.strftime("%Y-%m-%d")
        }
      rescue => e
        { error: "弱点分析に失敗しました: \#{e.message}" }
      end
    RUBY
  end

  def check_learning_progress_definition
    <<~RUBY
      def check_learning_progress(period: "month")
        user = Thread.current[:current_user] || raise("ユーザー情報が取得できません")

        start_date = case period
                     when "week" then 1.week.ago
                     when "month" then 1.month.ago
                     when "year" then 1.year.ago
                     else 1.month.ago
                     end

        enrollment_lessons = user.enrollment_lessons.where("enrollment_lessons.created_at >= ?", start_date)
        completed_lessons = enrollment_lessons.where.not(finished_at: nil)

        enrollments = user.enrollments.where("enrollments.created_at >= ?", start_date)
        completed_courses = enrollments.select(&:finished?)

        total_lesson_count = enrollment_lessons.sum(:count) || 0

        avg_lesson_completion = enrollment_lessons.any? ? (completed_lessons.count.to_f / enrollment_lessons.count * 100) : 0
        avg_course_completion = enrollments.any? ? (completed_courses.size.to_f / enrollments.count * 100) : 0

        days = case period
               when "week" then 7
               when "month" then 30
               when "year" then 365
               else 30
               end

        current_date = Date.current
        streak = 0

        while current_date >= start_date.to_date
          has_activity = user.enrollment_lessons
                            .where(enrollment_lessons: { created_at: current_date.beginning_of_day..current_date.end_of_day })
                            .exists?

          if has_activity
            streak += 1
            current_date -= 1.day
          else
            break
          end
        end

        activity_by_day = enrollment_lessons
                           .group("DATE(enrollment_lessons.created_at)")
                           .count

        most_active_day = if activity_by_day.empty?
                           "活動なし"
                         else
                           most_active = activity_by_day.max_by { |date, count| count }
                           "\#{most_active[0]}（\#{most_active[1]}回の活動）"
                         end

        {
          period: period,
          start_date: start_date.strftime("%Y-%m-%d"),
          end_date: Time.current.strftime("%Y-%m-%d"),
          total_lessons_started: enrollment_lessons.count,
          completed_lessons: completed_lessons.count,
          lesson_completion_rate: enrollment_lessons.count > 0 ? ((completed_lessons.count.to_f / enrollment_lessons.count) * 100).round(1) : 0,
          average_lesson_completion: avg_lesson_completion.round(1),
          total_courses_started: enrollments.count,
          completed_courses: completed_courses.size,
          course_completion_rate: enrollments.count > 0 ? ((completed_courses.size.to_f / enrollments.count) * 100).round(1) : 0,
          average_course_completion: avg_course_completion.round(1),
          total_lesson_count: total_lesson_count,
          average_daily_lessons: (total_lesson_count / days.to_f).round(1),
          overall_progress_score: ((avg_lesson_completion * 0.6) + (avg_course_completion * 0.4)).round(1),
          learning_streak: streak,
          most_active_day: most_active_day
        }
      rescue => e
        { error: "学習進捗の取得に失敗しました: \#{e.message}" }
      end
    RUBY
  end

  def get_daily_study_plan_definition
    <<~RUBY
      def get_daily_study_plan(target_minutes: 30)
        user = Thread.current[:current_user] || raise("ユーザー情報が取得できません")

        incomplete_lessons = user.enrollment_lessons
                                .joins(:lesson)
                                .where(finished_at: nil)
                                .includes(:lesson, :enrollment)
                                .limit(5)

        weak_exams = user.user_exams
                        .joins(:exam)
                        .finished
                        .where('allotment > 0')
                        .where('(score * 100.0 / allotment) < ?', 70)
                        .includes(:exam)
                        .limit(3)

        current_goals = user.user_goals
                           .where(completed_at: nil)
                           .includes(:goal)
                           .limit(3)

        study_plan = []
        remaining_minutes = target_minutes.to_i

        lesson_time = (remaining_minutes * 0.5).to_i
        if incomplete_lessons.any? && lesson_time > 0
          study_plan << {
            type: "レッスン学習",
            time_minutes: lesson_time,
            items: incomplete_lessons.map { |el| el.lesson.name },
            priority: "高",
            reason: "未完了のレッスンを優先的に進めましょう"
          }
          remaining_minutes -= lesson_time
        end

        review_time = (remaining_minutes * 0.6).to_i
        if weak_exams.any? && review_time > 0
          study_plan << {
            type: "復習・弱点克服",
            time_minutes: review_time,
            items: weak_exams.map { |ue| "\#{ue.exam.name} (平均点: \#{ue.average}%)" },
            priority: "中",
            reason: "苦手分野を重点的に復習しましょう"
          }
          remaining_minutes -= review_time
        end

        if current_goals.any? && remaining_minutes > 0
          study_plan << {
            type: "目標達成活動",
            time_minutes: remaining_minutes,
            items: current_goals.map { |ug| ug.goal.title },
            priority: "中",
            reason: "設定した目標に向けて取り組みましょう"
          }
        end

        {
          target_study_time: target_minutes,
          study_plan: study_plan,
          total_planned_time: study_plan.sum { |plan| plan[:time_minutes] },
          recommendations: [
            "短時間でも毎日継続することが重要です",
            "集中できる時間帯に学習しましょう",
            "休憩を適度に取りながら進めてください"
          ],
          created_at: Time.current.strftime("%Y-%m-%d %H:%M")
        }
      rescue => e
        { error: "学習計画の作成に失敗しました: \#{e.message}" }
      end
    RUBY
  end

  def analyze_learning_patterns_definition
    <<~RUBY
      def analyze_learning_patterns(days: 14)
        user = Thread.current[:current_user] || raise("ユーザー情報が取得できません")
        start_date = days.to_i.days.ago

        tracking_data = user.tracking_users
                           .where("tracking_date >= ?", start_date.to_date)
                           .order(:tracking_date)

        activities = user.activities
                        .where("created_at >= ?", start_date)
                        .includes(:object)

        daily_stats = tracking_data.group_by { |t| t.tracking_date.wday }.map do |wday, data|
          avg_minutes = data.sum(&:total_recorded_value) / 60.0 / data.size
          {
            day_of_week: Date::DAYNAMES[wday],
            average_study_minutes: avg_minutes.round(1),
            study_sessions: data.size
          }
        end

        hourly_activity = activities.group_by { |a| a.created_at.hour }
        peak_hours = hourly_activity.map { |hour, acts| [hour, acts.size] }
                                   .sort_by { |_, count| -count }
                                   .first(3)

        study_days = tracking_data.select { |t| t.total_recorded_value > 0 }.size
        total_days = (Date.current - start_date.to_date).to_i + 1
        consistency_rate = (study_days.to_f / total_days * 100).round(1)

        current_streak = 0
        date = Date.current
        while date >= start_date.to_date
          has_study = tracking_data.any? { |t| t.tracking_date == date && t.total_recorded_value > 0 }
          if has_study
            current_streak += 1
            date -= 1.day
          else
            break
          end
        end

        {
          analysis_period: "\#{start_date.strftime('%Y-%m-%d')} - \#{Date.current.strftime('%Y-%m-%d')}",
          daily_patterns: daily_stats.sort_by { |d| Date::DAYNAMES.index(d[:day_of_week]) },
          peak_study_hours: peak_hours.map { |hour, count| "\#{hour}時台 (\#{count}回の活動)" },
          consistency_rate: consistency_rate,
          current_streak: current_streak,
          total_study_time: (tracking_data.sum(&:total_recorded_value) / 60.0).round(1),
          insights: ["学習パターンの分析結果です"],
          recommendations: ["継続的な学習を心がけましょう"]
        }
      rescue => e
        { error: "学習パターン分析に失敗しました: \#{e.message}" }
      end
    RUBY
  end

  def get_motivation_boost_definition
    <<~RUBY
      def get_motivation_boost
        user = Thread.current[:current_user] || raise("ユーザー情報が取得できません")

        recent_lessons = user.enrollment_lessons
                            .where("finished_at >= ?", 7.days.ago)
                            .includes(:lesson)

        recent_exams = user.user_exams
                          .where("finished_at >= ?", 7.days.ago)
                          .where("average >= ?", 70)
                          .includes(:exam)

        current_date = Date.current
        streak = 0
        while current_date >= (Date.current - 30.days)
          has_activity = user.activities
                            .where(created_at: current_date.beginning_of_day..current_date.end_of_day)
                            .exists?
          if has_activity
            streak += 1
            current_date -= 1.day
          else
            break
          end
        end

        total_lessons_completed = user.enrollment_lessons.where.not(finished_at: nil).count
        total_exams_passed = user.user_exams.where("average >= ?", 70).count

        achievements = []
        achievements << "この1週間で\#{recent_lessons.count}個のレッスンを完了しました！" if recent_lessons.any?
        achievements << "この1週間で\#{recent_exams.count}個の試験に合格しました！" if recent_exams.any?
        achievements << "\#{streak}日連続で学習を継続中です！" if streak > 0
        achievements << "これまでに\#{total_lessons_completed}個のレッスンを完了しています" if total_lessons_completed > 0
        achievements << "これまでに\#{total_exams_passed}個の試験に合格しています" if total_exams_passed > 0

        motivational_messages = []
        if streak >= 7
          motivational_messages << "素晴らしい継続力です！この調子で頑張りましょう！"
        elsif streak >= 3
          motivational_messages << "良いペースで学習を続けていますね！"
        else
          motivational_messages << "今日から新しいスタートを切りましょう！"
        end

        if recent_lessons.any? || recent_exams.any?
          motivational_messages << "最近の成果が素晴らしいです。この勢いを維持しましょう！"
        end

        motivational_messages << "小さな一歩の積み重ねが大きな成果につながります"
        motivational_messages << "あなたの努力は必ず実を結びます"

        next_steps = []
        incomplete_lessons = user.enrollment_lessons.where(finished_at: nil).limit(3)
        if incomplete_lessons.any?
          next_steps << "次は「\#{incomplete_lessons.first.lesson.name}」に挑戦してみましょう"
        end

        current_goals = user.user_goals.where(completed_at: nil).limit(2)
        if current_goals.any?
          next_steps << "目標「\#{current_goals.first.goal.title}」に向けて一歩ずつ進みましょう"
        end

        {
          achievements: achievements,
          motivational_messages: motivational_messages,
          next_steps: next_steps,
          learning_streak: streak,
          encouragement: "あなたの学習への取り組みは素晴らしいです。継続は力なり！",
          generated_at: Time.current.strftime("%Y-%m-%d %H:%M")
        }
      rescue => e
        { error: "モチベーション情報の取得に失敗しました: \#{e.message}" }
      end
    RUBY
  end

  def analyze_learning_progress_definition
    <<~RUBY
      def analyze_learning_progress
        user = Thread.current[:current_user] || raise("ユーザー情報が取得できません")

        total_exams = user.user_exams.where(test_mode: false).where.not(finished_at: nil).count
        passed_exams = user.user_exams.joins(:exam)
                           .where(test_mode: false)
                           .where.not(finished_at: nil)
                           .where('user_exams.allotment > 0 AND round(user_exams.score * 100 / user_exams.allotment, 1) >= exams.pass_score')
                           .count

        active_goals = user.user_goals.where(completed_at: nil).count
        completed_goals = user.user_goals.where.not(completed_at: nil).count

        analysis = []
        analysis << "学習進捗分析:"
        analysis << ""
        analysis << "目標: \#{active_goals}件進行中、\#{completed_goals}件完了"
        analysis << "試験: \#{total_exams}件受験、\#{passed_exams}件合格"

        if passed_exams > 0 && total_exams > 0
          pass_rate = (passed_exams.to_f / total_exams * 100).round(1)
          analysis << "合格率: \#{pass_rate}%"

          if pass_rate >= 80
            analysis << "素晴らしい成績です！この調子で頑張りましょう"
          elsif pass_rate >= 60
            analysis << "良い成績です。さらなる向上を目指しましょう"
          else
            analysis << "復習に重点を置いて学習を進めましょう"
          end
        end

        analysis.join("\\n")
      rescue => e
        "学習進捗の分析に失敗しました: \#{e.message}"
      end
    RUBY
  end

  def get_motivation_message_definition
    <<~RUBY
      def get_motivation_message
        user = Thread.current[:current_user] || raise("ユーザー情報が取得できません")

        recent_passed_exams = user.user_exams.joins(:exam)
                                 .where(test_mode: false)
                                 .where('user_exams.finished_at >= ?', 7.days.ago)
                                 .where('user_exams.allotment > 0 AND round(user_exams.score * 100 / user_exams.allotment, 1) >= exams.pass_score')
                                 .count

        completed_goals = user.user_goals.where('completed_at >= ?', 30.days.ago).count

        messages = [
          "毎日の小さな努力が大きな成果につながります！",
          "継続は力なり。今日も一歩前進しましょう！",
          "目標に向かって着実に進んでいますね！",
          "学習は自分への最高の投資です！",
          "今日学んだことが明日の自信になります！"
        ]

        motivation = messages.sample

        if recent_passed_exams > 0
          motivation += "\\n\\n最近\#{recent_passed_exams}件の試験に合格されました。素晴らしいです！"
        end

        if completed_goals > 0
          motivation += "\\n\\nこの1ヶ月で\#{completed_goals}件の目標を達成されました。"
        end

        motivation += "\\n\\n今日も頑張りましょう！"

        motivation
      rescue => e
        "モチベーションメッセージの取得に失敗しました: \#{e.message}"
      end
    RUBY
  end

  def get_lesson_summary_definition
    <<~RUBY
      def get_lesson_summary(lesson_id: nil)
        user = Thread.current[:current_user] || raise("ユーザー情報が取得できません")

        unless lesson_id
          return "レッスンIDが指定されていません"
        end

        lesson = Lesson.find_by(id: lesson_id)
        return "レッスンが見つかりません" unless lesson

        summary = []
        summary << "\#{lesson.name}"
        summary << ""
        summary << "概要: \#{lesson.description}" if lesson.description.present?

        if lesson.course
          summary << "コース: \#{lesson.course.name}"
        end

        if lesson.body.present?
          content_preview = lesson.body.gsub(/<[^>]*>/, '').first(200)
          summary << ""
          summary << "内容:"
          summary << content_preview + "..."
        end

        summary.join("\\n")
      rescue => e
        "レッスン概要の取得に失敗しました: \#{e.message}"
      end
    RUBY
  end

  def explain_concept_definition
    <<~RUBY
      def explain_concept(concept: nil)
        return "説明する概念が指定されていません" unless concept

        "「\#{concept}」について説明します:\\n\\nこの概念についてより詳しく学習するには、関連するレッスンを確認することをお勧めします。具体的な質問があれば、お気軽にお聞きください。"
      rescue => e
        "概念説明の生成に失敗しました: \#{e.message}"
      end
    RUBY
  end

  def check_understanding_definition
    <<~RUBY
      def check_understanding(topic: nil)
        return "確認するトピックが指定されていません" unless topic

        questions = [
          "\#{topic}の主要なポイントを3つ挙げてください",
          "\#{topic}を実際の場面でどのように活用できますか？",
          "\#{topic}について疑問に思うことはありますか？"
        ]

        "\#{topic}の理解度チェック:\\n\\n" + questions.map.with_index(1) { |q, i| "\#{i}. \#{q}" }.join("\\n")
      rescue => e
        "理解度チェックの生成に失敗しました: \#{e.message}"
      end
    RUBY
  end
end
