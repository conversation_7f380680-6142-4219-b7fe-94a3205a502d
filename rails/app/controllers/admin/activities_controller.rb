class Admin::ActivitiesController < Admin::ApplicationController
  # :nocov:
  load_resource :school
  before_action :ensure_activity_enabled
  before_action :school_onboarding_detect
  authorize_resource :school, if: -> {@school.present?}
  load_resource :goal, through: :school, if: -> {@school.present?}
  load_resource :user
  load_resource :user_goal
  load_and_authorize_resource through: [:goal, :school], if: -> {@school.present?}
  load_and_authorize_resource through: :user, if: -> {@school.blank? && current_user.is_admin?}
  set_and_decorate_and_authorize_resource :parent, :goal

  add_breadcrumb -> { @user.name }, path: [:admin, :@school, :@user], if: ->{ @user.present? }
  add_breadcrumb -> { "アクティビティ一覧" }, path: [:admin, :@school, :activities]

  layout :layout_by_scope
  def index
    @user ||= @user_goal&.user
    @goal ||= @user_goal&.goal
    @activities = @activities.where(user: @user) if @school.present? && @user.present?
    @activities = @activities.includes(:user).latest.page params[:page]
    respond_to do |format|
      format.js
      format.html {
        if @user.present?
          return render "activities"
        end
      }
    end
  end

  def ensure_activity_enabled
    unless @school.use_activities?
      render plain: "このページにアクセスする権限がありません。" and return
    end
  end
end
