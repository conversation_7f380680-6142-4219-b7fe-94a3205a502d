class Admin::GoalsController < Admin::ApplicationController
  load_resource :school
  before_action :ensure_goal_enabled
  before_action :school_onboarding_detect
  authorize_resource :school, if: -> { @school.present? }
  load_and_authorize_resource through: :school, if: -> { @school.present? }
  before_action :limit_create, only: :create
  load_resource if: -> { current_user.is_admin? && @school.blank? }
  load_resource :user
  layout "admin_material"

  add_breadcrumb -> { "ゴール一覧" }, path: [:admin, :@school, :goals]
  add_breadcrumb "新規作成", only: [:new, :create]
  add_breadcrumb "ゴール編集", only: [:edit, :update]

  def index
    @goals = @goals.find_with_name(params[:q]) if params[:q].present?
    @goals = @goals.page(params[:page]).per(Settings.paginations.default_perpage)
    respond_to do |format|
      format.html
      format.js do
        if params[:premium_service_id].present?
          @premium_service = PremiumService.find(params[:premium_service_id])
          render "admin/premium_services/search_goals"
        else
         render "admin/user_goals/search_goals"
        end
      end
    end
  end

  def show
    # redirect temporary
    redirect_to [:admin, @school, @goal, :master_milestones]

    @assessments = Assessment.includes(skill_item: :skill).where(user_goal: @goal.user_goals)
    @master_milestones = @goal.master_milestones.includes(:milestones).order(:target_date)
    @milestones = Milestone.where(user_goal: @goal.user_goals)
    @exams = @goal.exams.includes(:user_exams).where("user_exams.finished_at is not null").references(:user_exams)
    @assignments = @goal
      .assignments
      .includes(:user_assignments)
      .where(assignmentable_type: "MasterMilestone")
      .where("user_assignments.completed_at is not null")
      .references(:user_assignments)
    @activities = @goal.activities.includes(:user).latest.page params[:page]
  end

  def new
  end

  def edit
    @skill_items = SkillItem.where(skill: @goal.skills)
  end

  def create
    @goal.user = current_user
    @goal.school = @school

    respond_to do |format|
      if @goal.save
        format.html { redirect_to process_polymophic(admin_school_goal_url(@school, @goal)), notice: t("message.create") }
        format.json { render :show, status: :created, location: @goal }
      else
        format.html { render :new }
        format.json { render json: @goal.errors, status: :unprocessable_entity }
      end
    end
  end

  def update
    respond_to do |format|
      if @goal.update(goal_params)
        format.html { redirect_to process_polymophic(admin_school_goal_url(@school, @goal)), notice: t("message.update") }
        format.json { render :show, status: :ok, location: @goal }
      else
        format.html { render :edit }
        format.json { render json: @goal.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @goal.destroy
    respond_to do |format|
      format.html { redirect_to process_polymophic(admin_school_goals_url(@school)), notice: t("message.destroy") }
      format.json { head :no_content }
    end
  end

  def chat_room
    @goal = Goal.find(params[:goal_id])
    redirect_to [:admin, @goal.project.school, @goal, :chat_rooms]
  end

  def import
    @goal = Goal.find(params[:id])

    if @goal.import(params[:file])
      redirect_to [:admin, @school, @goal], notice: "件のインポートが完了しました"
    else
      redirect_to [:admin, @school, @goal], alert: "インポートに失敗しました"
    end
  end

  private

  def limit_create
    raise CanCan::AccessDenied.new("Can not create more goal!", :create, Goal) unless @school.limitation.can_add_more_goals?
  end

  def goal_params
    params.require(:goal).permit(:name, :body, :description, :project_id, :user_id, :image, :type_is, :level, :passing_score,
      :premium_service_id, :published, :project, :school_id, :start_date, :color, skill_item_ids: [], goal_limitation_attributes: [:id, :users])
  end

  def ensure_goal_enabled
    unless @school.use_goal?
      render plain: "このページにアクセスする権限がありません。" and return
    end
  end
end
