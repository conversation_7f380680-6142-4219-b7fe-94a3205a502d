class Admin::UserQuestionsController < Admin::ApplicationController
  load_and_authorize_resource :school
  before_action :school_onboarding_detect
  load_resource :user
  load_resource :user_goal
  layout :set_layout

  add_breadcrumb -> { 'ユーザー一覧' }, path: [:admin, :@school, :users]
  add_breadcrumb -> { @user.name }, path: [:admin, :@school, :@user], only: :index
  add_breadcrumb -> { '質問一覧' }, only: :index

  def index
    @user ||= @user_goal&.user
    @goal ||= @user_goal&.goal

    @questions = @user.questions.includes(:answers).latest.page(params[:page]).per(30)
    @categories = Category.has_questions.map do |category|
      {
        id: category.id,
        title: category.title,
        question_num: @questions.select{ |q| q.category_id == category.id }.size
      }
    end
    if @school.present?
      @courses = @school.courses.published.includes(:questions)
    else
      @courses = Course.published.includes(:questions)
    end

    if params[:category_id].present?
      @questions = @questions.where(category_id: params[:category_id]).page(params[:page]).per(30)
      @category = @categories.detect{ |c| c[:id] == params[:category_id].to_i }
    end

    if params[:course_id].present?
      @course = @courses.detect{ |c| c.id == params[:course_id].to_i }
      @questions = @course.questions.includes(:answers).where(user_id: @user.id).latest.page(params[:page]).per(30)
    end
  end

  private

  def set_layout
    return "admin" unless @school.present?
    "admin_material"
  end
end
