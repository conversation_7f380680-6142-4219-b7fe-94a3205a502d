class Admin::QuestionsController < Admin::ApplicationController
  include NotificationHelper
  load_and_authorize_resource :school
  before_action :ensure_question_enabled
  before_action :school_onboarding_detect
  load_resource :course
  load_resource :lesson
  load_resource :user
  load_resource through: [:lesson, :course, :school]
  set_and_decorate_and_authorize_resource :parent, [:course, :lesson]
  authorize_resource :parent, if: ->{ @parent.present? }
  authorize_resource
  before_action :mark_as_read_question, only: :show
  before_action :set_courses, only: [:index, :show]

  layout 'admin_material'

  add_breadcrumb -> { @user.name }, path: [:admin, :@school, :@user], if: ->{ @user.present? }
  add_breadcrumb -> { "質問一覧" }, path: [:admin, :@school, :questions]
  add_breadcrumb -> { @question.title }, only: [:show]

  def index
    @questions = @questions.filter_by_query(params[:q]) if params[:q].present?

    if @course.present?
      @course_lessons = @course.course_lessons.joins(:lesson).includes(lesson: :questions).where.not(lessons: {published_at: nil}).rank(:row_order)
    end

    if @lesson.present?
      @course = @lesson.courses.first
      @course_lessons = @course.course_lessons.joins(:lesson).includes(lesson: :questions).where.not(lessons: {published_at: nil}).rank(:row_order) if @course.present?
    end

    if @user.present?
      @questions = @questions.includes(:answers).latest.where(user_id: @user.id).page(params[:page]).per(30)
      return render "questions"
    end
    @courses = @school.courses.pluck(:id, :name)
    if params[:courseId]
      @relation_course = Course.find(params[:courseId])
      @questions = @relation_course.questions
      @course_name = @courses.select{|x| x[0].to_s == params[:courseId]}[0][1]
    end
    @questions = @questions.includes(:answers).latest.page(params[:page]).per(30)
  end

  def show
    obj =
    if @course
      @course
    elsif @goal
      @goal
    elsif @school
      @school
    end
    @un_answerd_questions = obj.questions.includes(:answers)
      .where(answers: {question_id: nil})
      .where.not(id: @question.id)
      .order(created_at: :desc)

    question_relatives = @question.question_relatives
    if question_relatives.present?
      @lesson_relatives = Lesson.where(id: question_relatives.where(relativeable_type: "Lesson").pluck(:relativeable_id))
      @relative_lesson_questions = Question.shared.includes(:question_relatives)
        .where(question_relatives: {relativeable_type: "Lesson", relativeable: @lesson_relatives})
        .where.not(id: @question.id)
        .uniq
      @course = Course.find(question_relatives.where(relativeable_type: "Course").first.relativeable_id)
      @lesson = @lesson_relatives.first if @question.category.ctype == "lessons" && @lesson_relatives.present?
      @course_lessons = @course.course_lessons.joins(:lesson).includes(lesson: :questions).where.not(lessons: {published_at: nil}).rank(:row_order)
    end
  end

  def edit
  end

  def update
    @question.update question_params

    respond_to do |format|
      format.js
    end
  end

  def destroy
    @question.destroy
    respond_to do |format|
      format.html { redirect_back(fallback_location: admin_school_questions_path(@school)) }
      format.js { redirect_to admin_school_questions_path(@school) }
    end
  end

  def search
    return unless params[:q].present?
    @questions = @questions.includes(:answers).filter_by_query(params[:q]).latest.limit(10)
  end

  private

  def question_params
    params.require(:question).permit(:title, :body, :is_deleted)
  end

  def set_courses
    @courses = @school.courses
      .published.left_joins(:questions)
      .group("courses.id")
      .order("COUNT(questions.id) DESC")
  end

  def ensure_question_enabled
    unless @school.use_question_list?
      render plain: "このページにアクセスする権限がありません。" and return
    end
  end
end
