class Api::V1::Classroom::NotificationsController < Api::V1::Classroom::ApplicationController
  def index
    current_user.check_notification
    headers['Last-Modified'] = Time.now.httpdate
    output, notifications = paginate current_user.notifications.latest
    output['notifications'] = notifications
    render json: output, status: :ok
  end

  swagger_controller :Notifications, "Notifications management"
  swagger_api :index do
    summary "Fetch notifications"
    consumes [ "application/json" ]

    response :ok, "Success", :Notification
  end

  swagger_model :Notification do
    description "Notification"
    property :id, :integer, :required, "Notification Id"
    property :target_type, :string, :required, "Target type"
    property :target_id, :string, :required, "Target id"
    property :action, :string, :required, "Action"
    property :created_at, :datetime, :optional, "Created date"
  end
end
