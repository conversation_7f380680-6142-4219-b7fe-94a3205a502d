class Api::V2::MessagesController < Api::V2::ApplicationController
  include ActionController::Live

  before_action :authenticate_user!

  def index
    targetable_id = params["targetable_id"]
    targetable_type = params["targetable_type"]
    user_id = current_user.id
    thread_id = ""
    school = nil

    if targetable_type == Lesson.name
      less = Lesson.find targetable_id
      school = less.school
    elsif targetable_type == Question.name
      q = Q.find(targetable_id)
      school = q.exams.first.school
    elsif targetable_type == Exam.name
      exam = Exam.find(targetable_id)
      school = exam.school
    elsif targetable_type == School.name
      school = School.find(targetable_id)
    end

    if params["thread_id"].present?
      thread_id = current_user.chat_threads.where(id: params["thread_id"])
    else
      threads = current_user.chat_threads.where(targetable_type: targetable_type, targetable_id: targetable_id.to_i)
      if threads.any? && threads.last.messages.empty?
        thread_id = threads.last.id
      else
        chat_thread = current_user.chat_threads.new
        chat_thread.school_id = school&.id
        chat_thread.targetable_id = create_params[:targetable_id]
        chat_thread.targetable_type = create_params[:targetable_type]
        chat_thread.name = "NewThread"
        chat_thread.save
        thread_id = chat_thread.id

        # Add welcome message for new thread
        if targetable_type == School.name
          add_welcome_message(chat_thread, school)
        end
      end
    end
    if targetable_type == Lesson.name
      less = Lesson.find targetable_id
      school = less.school
    elsif targetable_type == Question.name
      q = Q.find(targetable_id)
      school = q.exams.first.school
    elsif targetable_type == Exam.name
      exam = Exam.find(targetable_id)
      school = exam.school
    elsif targetable_type == School.name
      school = School.find(targetable_id)
    end
    messages = Message.where(to_user_id: user_id, targetable_type: targetable_type, targetable_id: targetable_id.to_i, chat_thread_id: thread_id).includes(:attachments)
    json_response(BaseSerializer.render_list(messages, MessageSerializer, "list"),
      meta: {
        total_message: @current_user.messages.where(created_at: Date.today.all_day).size,
        limit_message: ai_chat_daily_usage_limit || school&.limit_ai_messages || 10
      })
  end

  def show
    message = Message.where(to_user_id: current_user.id).includes(:attachments).find(params[:id])
    json_response(MessageSerializer.new(message, request_mode).render)
  end

  def speech
    message = Message.where(to_user_id: current_user.id).find(params[:id])

    json_response({
                    url: message.speech_attachment_url
                  })
  end

  def create
    targetable_id = create_params[:targetable_id]
    targetable_type = create_params[:targetable_type]
    school = nil
    if targetable_type == Lesson.name
      less = Lesson.find targetable_id
      school = less.school
      if create_params[:agent_id].blank?
        json_response({}, status: StatusCodeApi::CREATE_ERROR, message: "Admin not configured agent")
        return
      end
    elsif targetable_type == Question.name
      q = Q.find(targetable_id)
      school = q.exams.first.school
    elsif targetable_type == Exam.name
      exam = Exam.find(targetable_id)
      school = exam.school
    elsif targetable_type == School.name
      school = School.find(targetable_id)
      if create_params[:agent_id].blank?
        json_response({}, status: StatusCodeApi::CREATE_ERROR, message: "Admin not configured dashboard agent")
        return
      end
    end
    message_num = @current_user.messages.where(created_at: Date.today.all_day).size
    limit_message = ai_chat_daily_usage_limit || school&.limit_ai_messages || 10
    if message_num >= limit_message
      json_response({}, status: StatusCodeApi::CREATE_ERROR, message: "Message create error")
    else
      thread_id = nil
      if params["thread_id"].present?
        thread_id = current_user.chat_threads.find(params["thread_id"]).id
      else
        threads = current_user.chat_threads.where(targetable_type: targetable_type, targetable_id: targetable_id.to_i)
        if threads.any?
          thread_id = threads.last.id
        else
          chat_thread = current_user.chat_threads.new
          chat_thread.school_id = school&.id
          chat_thread.targetable_id = create_params[:targetable_id]
          chat_thread.targetable_type = create_params[:targetable_type]
          chat_thread.name = "NewThread"
          chat_thread.save
          thread_id = chat_thread.id

          # Add welcome message for new thread
          if targetable_type == School.name
            add_welcome_message(chat_thread, school)
          end
        end
      end
      message = Message.new
      message.targetable_id = targetable_id
      message.targetable_type = targetable_type
      message.chat_thread_id = thread_id
      message.user_id = current_user.id
      message.to_user_id = current_user.id
      message.owner_type = Message.owner_types["student"]
      message.meta_data = params[:metadata]
      message.agent_id = create_params[:agent_id] if create_params[:agent_id].present?
      ai_chat_question_id = create_params[:ai_chat_question_id]
      if ai_chat_question_id.present?
        ai_chat_question = AiChatQuestion.find(ai_chat_question_id)
        message.content = ai_chat_question.prompt
        message.content_overwrite = ai_chat_question.title

        ai_chat_question_user = AiChatQuestionUser.find_or_create_by(user_id: current_user.id, ai_chat_question_id: ai_chat_question.id)
        ai_chat_question_user.increment!(:clicks_count)

        if ai_chat_question.image.present?
          attachment = Attachment.create_from_string(URI.open(ai_chat_question.image_url).read,
                                                     filename: ai_chat_question[:image])
          message.attachment_ids = [attachment.id]
        end
      else
        message.content = create_params[:content]
        message.attachment_ids = params[:attachment_ids]
      end
      message.save
      json_response(MessageSerializer.new(message, request_mode).render)
    end
  end

  def clear
    targetable_id = params["targetable_id"]
    targetable_type = params["targetable_type"]
    user_id = current_user.id
    messages = Message.where(to_user_id: user_id, targetable_type: targetable_type, targetable_id: targetable_id.to_i)
    messages.destroy_all
    json_response(BaseSerializer.render_list({}, MessageSerializer, "list"))
  end

  def stop
    targetable_id = params["targetable_id"]
    targetable_type = params["targetable_type"]
    process_id = "generating_message_#{current_user.id}_#{targetable_type}_#{targetable_id}"
    Rails.cache.write(process_id, false)
    ActionCable.server.broadcast "user:#{current_user.id}:#{targetable_type}:#{targetable_id}:chats", {
      message_id: "",
      content: "Stop finished"
    }
  end

  def regenerate
    message = Message.where(to_user_id: current_user.id).find(params[:id])
    message.regenerate
    json_response({})
  end

  private
  def create_params
    params.permit(:content, :targetable_id, :targetable_type, :thread_id, :ai_chat_question_id, :agent_id,
                  attachment_ids: [], metadata: {}, message: {})
  end

  def ai_chat_daily_usage_limit
    @current_user.ai_chat_setting&.daily_usage_limit.to_i > 0 ? @current_user.ai_chat_setting&.daily_usage_limit.to_i : nil
  end

  def add_welcome_message(chat_thread, school)
    teacher_id = school.teachers.first&.id
    user_name = current_user.name.present? ? current_user.name : "友達"

    # Get appropriate agent based on targetable_type
    agent_id = get_default_agent_id(school, chat_thread.targetable_type)

    welcome_message = Message.create(
      user_id: teacher_id,
      targetable_id: chat_thread.targetable_id,
      targetable_type: chat_thread.targetable_type,
      chat_thread_id: chat_thread.id,
      school_id: school.id,
      content: "こんにちは#{user_name}さん、今日も頑張りましょう！",
      to_user_id: current_user.id,
      owner_type: Message.owner_types["bot"],
      skip_callback: true,
      agent_id: agent_id
    )
  end

  def get_default_agent_id(school, targetable_type)
    case targetable_type
    when 'School'
      school.ai_tutor_agents.enabled.find_by(agent_type: 'dashboard')&.id
    when 'Lesson'
      school.ai_tutor_agents.enabled.find_by(agent_type: 'lesson')&.id
    else
      school.ai_tutor_agents.enabled.first&.id
    end
  end
end
