class Api::V2::QuestionsController < Api::V2::ApplicationController
  before_action :authenticate_user!

  def index
    questions = SchoolManager.questions.includes(:user).shared.where("
        questions.title LIKE '%#{Question.sanitize_sql_like(params[:search] || '')}%'
        OR
        questions.body LIKE '%#{Question.sanitize_sql_like(params[:search] || '')}%'
      ")
    if relative_target
      questions = questions
        .joins(:question_relatives)
        .where(question_relatives: {
          relativeable_type: params[:relativeable_type],
          relativeable_id: params[:relativeable_id]
        })
    end

    response_list(questions)
  end

  def my
    response_list(
      SchoolManagerUser.questions.includes(:user)
      .where("
        questions.title LIKE '%#{Question.sanitize_sql_like(params[:search] || '')}%'
        OR
        questions.body LIKE '%#{Question.sanitize_sql_like(params[:search] || '')}%'
      ")
    )
  end

  def my_question_answers
    question_ids = Answer.where(user_id: SchoolManagerUser.current_user.id).pluck(:question_id)
    response_list(
      Question.includes(:user).where(id: question_ids)
      .where("
        questions.title LIKE '%#{Question.sanitize_sql_like(params[:search] || '')}%'
        OR
        questions.body LIKE '%#{Question.sanitize_sql_like(params[:search] || '')}%'
      ")
    )
  end

  def create
    question = Question.new(create_params)
    question.school_id = SchoolManager.main_school.id
    question.user_id = current_user.id
    question.save
    question.set_question_relatives(params[:course_id], params[:lesson_id])

    if question.question_relatives.present?
      relativeable_type = question.question_relatives.last.relativeable_type
      relativeable_id = question.question_relatives.last.relativeable_id
      broadcast_channel(relativeable_type, relativeable_id, "questions")
      broadcast_channel(relativeable_type, relativeable_id, "questions_count")
    end

    json_response(QuestionSerializer.new(question, request_mode).render)
  end

  def update
    question = Question.find(params[:id])

    if question.blank?
      json_response({}, status: StatusCodeApi::NOT_EXISTS, message: "Question not exist")
    elsif question.user_id != current_user.id
      json_response({}, status: StatusCodeApi::PERMISSION_DENIED, message: "PERMISSION_DENIED")
    else
      question.update(update_params)

      if question.question_relatives.present?
        relativeable_type = question.question_relatives.last.relativeable_type
        relativeable_id = question.question_relatives.last.relativeable_id
        broadcast_channel(relativeable_type, relativeable_id, "questions")
        broadcast_channel(relativeable_type, relativeable_id, "questions_count")
      end

      json_response(QuestionSerializer.new(question, request_mode).render)
    end
  end

  def destroy
    question = Question.find(params[:id])

    if question.present? && question.user_id != current_user.id
      json_response({}, status: StatusCodeApi::PERMISSION_DENIED, message: "PERMISSION_DENIED")
    else
      question.destroy if question.present?

      if question.question_relatives.present?
        relativeable_type = question.question_relatives.last.relativeable_type
        relativeable_id = question.question_relatives.last.relativeable_id
        broadcast_channel(relativeable_type, relativeable_id, "questions")
        broadcast_channel(relativeable_type, relativeable_id, "questions_count")
      end

      json_response({})
    end
  end

  def show
    question = SchoolManager.questions.find(params[:id])

    if current_user.present? && params[:nontrack].blank?
      Activity.create user: current_user, action: "show_question", object: question, school_id: question.school_id
    end

    json_response(QuestionSerializer.new(question, request_mode).render)
  end

  private

  def relative_target
    case params[:relativeable_type]
    when "Lesson"
      Lesson.find(params[:relativeable_id])
    when "Course"
      SchoolManager.courses.find(params[:relativeable_id])
    else
      nil
    end
  end

  def create_params
    prm = params.permit(:title, :body, :from, :tags, :category_id, :question_type, materials: [])
    prm[:category_id] = Category::OTHER_ID if prm[:category_id].blank?

    prm
  end

  def update_params
    params.permit(:title, :body, :question_type, materials: [])
  end

  def response_list(questions)
    questions = questions.latest.page(params[:page]).per(10)

    json_response(
      BaseSerializer.render_list(questions, QuestionSerializer, "list"),
      meta: {
        pagination: {
          page: questions.current_page,
          per_page: params[:per_page] || 30,
          total_pages: questions.total_pages,
          total_objects: questions.total_count
        }
      }
    )
  end

  def broadcast_channel(relativeable_type, relativeable_id, suffix_channel_name)
    channel = "#{relativeable_type}:#{relativeable_id}:#{suffix_channel_name}"
    ActionCable.server.broadcast channel, {
      content: "New question come"
    }
  end
end
