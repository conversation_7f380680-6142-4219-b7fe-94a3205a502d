class Api::V2::ChatThreadsController < Api::V2::ApplicationController
  before_action :authenticate_user!

  def index
    chat_threads = current_user.chat_threads.not_playground.distinct.joins(:messages)
                    .where(targetable_type: params["targetable_type"], targetable_id: params["targetable_id"])
                    .order(created_at: :desc)
    json_response(BaseSerializer.render_list(chat_threads, ChatThreadSerializer, "list"))
  end

  def create
    chat_thread = current_user.chat_threads.new
    chat_thread.school_id = SchoolManager.main_school.id
    chat_thread.targetable_id = create_params[:targetable_id]
    chat_thread.targetable_type = create_params[:targetable_type]
    chat_thread.name = "New Thread"
    chat_thread.save

    # Add welcome message
    if chat_thread.targetable_type == "School"
      add_welcome_message(chat_thread)
    end

    json_response(ChatThreadSerializer.new(chat_thread, request_mode).render)
  end

  def update
    chat_thread = current_user.chat_threads.find(params[:id])
    chat_thread.update(name: params[:name]) if chat_thread.present?
    json_response({})
  end

  def destroy
    chat_thread = current_user.chat_threads.find(params[:id])
    chat_thread.destroy if chat_thread.present?
    json_response({})
  end

  private

  def create_params
    params.permit(:targetable_id, :targetable_type)
  end

  def add_welcome_message(chat_thread)
    school = SchoolManager.main_school
    teacher_id = school.teachers.first&.id
    user_name = current_user.name.present? ? current_user.name : "友達"

    # Get appropriate agent based on targetable_type
    agent_id = get_default_agent_id(school, chat_thread.targetable_type)

    welcome_message = Message.create(
      user_id: teacher_id,
      targetable_id: chat_thread.targetable_id,
      targetable_type: chat_thread.targetable_type,
      chat_thread_id: chat_thread.id,
      school_id: school.id,
      content: "こんにちは#{user_name}さん、今日も頑張りましょう！",
      to_user_id: current_user.id,
      owner_type: Message.owner_types["bot"],
      skip_callback: true,
      agent_id: agent_id
    )
  end

  def get_default_agent_id(school, targetable_type)
    case targetable_type
    when 'School'
      school.ai_tutor_agents.enabled.find_by(agent_type: 'dashboard')&.id
    when 'Lesson'
      school.ai_tutor_agents.enabled.find_by(agent_type: 'lesson')&.id
    else
      school.ai_tutor_agents.enabled.first&.id
    end
  end
end
