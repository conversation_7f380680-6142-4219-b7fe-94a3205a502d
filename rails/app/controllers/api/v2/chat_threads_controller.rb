class Api::V2::ChatThreadsController < Api::V2::ApplicationController
  before_action :authenticate_user!

  def index
    chat_threads = current_user.chat_threads.not_playground.distinct.joins(:messages)
                    .where(targetable_type: params["targetable_type"], targetable_id: params["targetable_id"])
                    .order(created_at: :desc)
    json_response(BaseSerializer.render_list(chat_threads, ChatThreadSerializer, "list"))
  end

  def create
    chat_thread = current_user.chat_threads.new
    chat_thread.school_id = SchoolManager.main_school.id
    chat_thread.targetable_id = create_params[:targetable_id]
    chat_thread.targetable_type = create_params[:targetable_type]
    chat_thread.name = "New Thread"
    chat_thread.save
    json_response(ChatThreadSerializer.new(chat_thread, request_mode).render)
  end

  def update
    chat_thread = current_user.chat_threads.find(params[:id])
    chat_thread.update(name: params[:name]) if chat_thread.present?
    json_response({})
  end

  def destroy
    chat_thread = current_user.chat_threads.find(params[:id])
    chat_thread.destroy if chat_thread.present?
    json_response({})
  end

  private

  def create_params
    params.permit(:targetable_id, :targetable_type)
  end
end
