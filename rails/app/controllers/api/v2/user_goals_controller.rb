class Api::V2::UserGoalsController < Api::V2::ApplicationController
  before_action :authenticate_user!

  def index
    user_goals = SchoolManagerUser.user_goals.includes(:goal).order(score: :desc).where("
                user_goals.name LIKE '%#{UserGoal.sanitize_sql_like(params[:search] || '')}%'
                OR
                user_goals.description LIKE '%#{UserGoal.sanitize_sql_like(params[:search] || '')}%'
              ")
    json_response(BaseSerializer.render_list(user_goals, UserGoalSerializer, request_mode))
  end

  def show
    user_goal = SchoolManagerUser.user_goals.find_by_id(params[:id])
    return json_response({}, status: StatusCodeApi::NOT_EXISTS, message: "User goal not exist", status: 404) unless user_goal
    json_response(UserGoalSerializer.new(user_goal, "detail").render)
  end

  def current_goal
    if SchoolManagerUser.user_goals.valid.blank?
      json_response(nil)
    else
      user_goal = SchoolManagerUser.activities.joins(:goal).for_users(current_user.id).latest.detect do |activity|
        user_goal = activity.goal.user_goal_by_user(current_user)
        target_date = user_goal&.milestones&.target_date_exist&.max_by(&:target_date)&.target_date&.to_date
        next if target_date.blank?
        target_date >= Date.today
      end&.goal&.user_goal_by_user(current_user)
      json_response(user_goal.present? ? UserGoalSerializer.new(user_goal, "detail").render : nil)
    end
  end

  def save_activity
    UserGoal.find(params[:id]).save_activity(data: "interacted")
    json_response({})
  end
end
