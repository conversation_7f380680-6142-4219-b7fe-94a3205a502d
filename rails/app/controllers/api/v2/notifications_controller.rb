class Api::V2::NotificationsController < Api::V2::ApplicationController
  before_action :set_notifications
  before_action :authenticate_user!

  def index
    if params[:check_notification] && params[:check_notification] == 'true'
      current_user.check_notification
    end

    json_response(
      {
        unread: @notifications.unread.size,
        notifications: BaseSerializer.render_list(@notifications.includes(target: [:user, :commentable]).latest, NotificationSerializer, request_mode)
      }
    )
  end

  def mark_readed
    @notifications.find(params[:id])&.mark_as_read
    json_response({})
  end

  private

  def set_notifications
    @notifications = current_user.notifications.where(
      school_id: SchoolManager.main_school.id
    ).where.not(
      action: 'subscription_create'
    )
  end
end
