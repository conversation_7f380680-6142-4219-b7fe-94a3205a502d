class Classroom::NotificationsController < Classroom::ApplicationController
  before_action :set_notifications
  layout 'classroom'
  def index
    current_user.check_notification
    headers['Last-Modified'] = Time.now.httpdate

    @notification_path = "classroom/notifications/actions/"
    @notification_path = "common/notifications/" if current_user.is_admin?

    respond_to do |format|
      format.html do
        @notifications = @notifications.page(params[:page]).per Settings.paginations.default_perpage
      end
      format.js
    end
  end

  private
  def set_notifications
    @notifications = SchoolManagerUser.notifications.latest
  end
end
