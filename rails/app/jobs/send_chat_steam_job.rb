class SendChatSteamJob < ApplicationJob
  queue_as :default

  def perform(messages, object, process_id, system_message_content: nil, agent_id: nil)
    sleep(1)
    Rails.cache.write(process_id, true)

    if object.targetable_type == "Lesson"
      LessonChat.chat_steam(messages, object, process_id, system_message_content: system_message_content, agent_id: agent_id)
    elsif object.targetable_type == "School"
      DashboardChat.chat_steam(messages, object, process_id, system_message_content: system_message_content, agent_id: agent_id)
    else
      CustomChat.chat_steam(messages, object, process_id, system_message_content: system_message_content)
    end
  rescue => e
    Rails.logger.error "SendChatSteamJob error: #{e.message}"
    Rails.logger.error "Backtrace: #{e.backtrace.first(5).join('\n')}"
    raise e
  end
end
