export default class MessageForm {
  public content: string = ''
  public targetableId: number
  public targetableType: string = ''
  public threadId: string = ''
  public attachmentIds: number[] = []
  public metadata: any = {}
  public aiChatQuestionId: number = null
  public agentId: number = null
  public message?: { content: string } // Support legacy format

  constructor(data?: any) {
    if (data) {
      // Handle legacy format { message: { content: string } }
      if (data.message && data.message.content) {
        this.content = data.message.content
      } else if (data.content) {
        this.content = data.content
      }

      // Set other properties
      if (data.targetableId) this.targetableId = data.targetableId
      if (data.targetableType) this.targetableType = data.targetableType
      if (data.threadId) this.threadId = data.threadId
      if (data.attachmentIds) this.attachmentIds = data.attachmentIds
      if (data.metadata) this.metadata = data.metadata
      if (data.aiChatQuestionId) this.aiChatQuestionId = data.aiChatQuestionId
      if (data.agentId) this.agentId = data.agentId
    }
  }
}
