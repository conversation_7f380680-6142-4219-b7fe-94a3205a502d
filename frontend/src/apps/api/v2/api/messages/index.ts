import BaseApi from '../base/base'
import Message from '../../models/message'
import MessageForm from '../../forms/message'

export default class MessageApi extends BaseApi {
  constructor() {
    super()
    this.path = '/messages'
  }
  async index(params?: {targetableId?: string, targetableType?: string, threadId?: string}) {
    this.path = '/messages'
    const response = await this.getRequest(params || {})
    return [
      response.meta,
      response.data.map((element: any) => {
        return new Message(element)
      })
    ]
  }
  async clear(params: {targetableId: string, targetableType: string}) {
    this.path = '/messages/clear'
    const response = await this.postRequest(params)
    return response.data.map((element: any) => {
      return new Message(element)
    })
  }

  async stopGenerate(params: {targetableId: string, targetableType: string}) {
    this.path = '/messages/stop'
    const response = await this.postRequest(params)
    return response.data.map((element: any) => {
      return new Message(element)
    })
  }

  async create(params: MessageForm | any) {
    this.path = '/messages'
    // Handle both MessageForm and legacy format
    let formData: MessageForm
    if (params instanceof MessageForm) {
      formData = params
    } else {
      formData = new MessageForm(params)
    }
    const response = await this.postRequest(formData)
    return new Message(response.data)
  }

  async show(id: number) {
    this.path = `/messages/${id}`
    const response = await this.getRequest()
    return new Message(response.data)
  }

  async regenerate(id: number) {
    this.path = `/messages/${id}/regenerate`
    const response = await this.getRequest()
    return response.apiStatus
  }

  async speech(id: number) {
    this.path = `/messages/${id}/speech`
    return await this.getRequest()
  }
}
