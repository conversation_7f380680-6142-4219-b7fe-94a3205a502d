import Exam from './exam'
import AdminExam from './admin/exam'
import ExamRevisionApi from './admin/exam_revision'
import As from './as'
import ExamStatusApi from './exam_status'
import UserApi from './user'
import NotificationApi from './notifications'
import SubscriptionApi from './subscriptions'
import CourseApi from './course'
import CourseLessonApi from './course_lesson'
import CategoryApi from './category'
import LessonApi from './lesson'
import EnrollmentApi from './enrollment'
import ChapterApi from './chapter'
import UserQuestionApi from './user_question'
import CommentApi from './comment'
import AnswerApi from './answers'
import VoteApi from './vote'
import TrackingApi from './trackings'
import ExamPurchaseApi from './exam_purchase'
import CoursePurchaseApi from './course_purchase'
import SchoolSettingApi from './school_setting'
import UserBlockApi from './user_block'
import UserGoalApi from './user_goal'
import ExamQuestionApi from './exam_questions'
import UserExamApi from './user_exams'
import CourseTagApi from './course_tag'
import SpeakAudioApi from './speak_audio'
import InforApi from './infors'
import BannerApi from './banner'
import MenuApi from './menu'
import ActivityApi from './activities'
import InquiryApi from './inquiries'
import ReviewApi from './reviews'
import MessageApi from './messages'
import ThreadApi from './threads'
import EmbedApi from './embed'
import UserOnboardApi from './user_onboard'
import ExamGenerationApi from './admin/exam_generation'
import AttachmentApi from "./attachment";
import AiChatQuestionApi from "./ai_chat_questions";
import AiTutorAgentApi from "./ai_tutor_agents";
import LiveChatMessageApi from "./live_chat_message";
import CustomTextApi from "./custom_texts";
import StripeTokenApi from "./stripe_token";

export default {
  admin: {
    exam: new AdminExam(),
    examRevision: new ExamRevisionApi(),
    examGeneration: new ExamGenerationApi()
  },
  exam: new Exam(),
  as: new As(),
  examStatus: new ExamStatusApi(),
  user: new UserApi(),
  notification: new NotificationApi(),
  subscription: new SubscriptionApi(),
  category: new CategoryApi(),
  banner: new BannerApi(),
  course: new CourseApi(),
  courseLesson: new CourseLessonApi(),
  lesson: new LessonApi(),
  enrollment: new EnrollmentApi(),
  chapter: new ChapterApi(),
  userQuestion: new UserQuestionApi(),
  review: new ReviewApi(),
  comment: new CommentApi(),
  answer: new AnswerApi(),
  vote: new VoteApi(),
  tracking: new TrackingApi(),
  examPurchase: new ExamPurchaseApi(),
  coursePurchase: new CoursePurchaseApi(),
  schoolSetting: new SchoolSettingApi(),
  userBlock: new UserBlockApi(),
  userGoal: new UserGoalApi(),
  examQuestionApi: new ExamQuestionApi(),
  userExam: new UserExamApi(),
  courseTag: new CourseTagApi(),
  speakAudio: new SpeakAudioApi(),
  infor: new InforApi(),
  inquiry: new InquiryApi(),
  menu: new MenuApi(),
  activity: new ActivityApi(),
  embed: new EmbedApi(),
  message: new MessageApi(),
  thread: new ThreadApi(),
  userOnboard: new UserOnboardApi(),
  attachment: new AttachmentApi(),
  aiChatQuestionApi: new AiChatQuestionApi(),
  aiTutorAgent: new AiTutorAgentApi(),
  liveChatMessage: new LiveChatMessageApi(),
  customText: new CustomTextApi(),
  stripeToken: new StripeTokenApi(),
  dashboardMessages: new MessageApi(), // Alias for dashboard chat messages
}
