import BaseApi from '../base/base'
import UserGoal from '../../models/user_goal'

export default class UserGoalApi extends BaseApi {
  constructor() {
    super()
    this.path = '/user_goals'
  }

  async index(params = {}) {
    this.path = '/user_goals'
    const response = await this.getRequest(params)
    return response.data.filter((item: any) => item != null).map((item: any) => new UserGoal(item))
  }

  async show(userGoalId: string, params = {}) {
    this.path = `/user_goals/${userGoalId}`
    const response = await this.getRequest(params)
    return new UserGoal(response.data)
  }

  async saveActivity(userGoalId: string, params = {}) {
    this.path = `/user_goals/${userGoalId}/save_activity`
    this.postRequest(params)
    return
  }

  async currentGoal(params = {}) {
    this.path = '/user_goals/current_goal'
    const response = await this.getRequest(params)
    if (!response.data) {
      return null
    }
    return new UserGoal(response.data)
  }
}
