import Goal from './goal'
import Image from './image'
import { differenceInDays } from 'date-fns'
class MilestoneUserExam {
  readonly id: number
  readonly userId: number
  readonly corrects: number
  readonly score: number
  readonly average: number
  readonly finishedAt: Date | undefined
  readonly createdAt: Date
  readonly startedAt: Date
  readonly updatedAt: string

  constructor(data: { [key: string]: any }) {
    this.id = data.id
    this.userId = data.userId
    this.corrects = data.corrects
    this.score = data.score
    this.average = data.average
    this.createdAt = new Date(data.createdAt)
    this.updatedAt = data.updatedAt
    if (data.finishedAt) {
      this.finishedAt = new Date(data.finishedAt)
    }
    if (data.startedAt) {
      this.startedAt = new Date(data.startedAt)
    }
  }
}

export class MilestoneExam {
  readonly id: number
  readonly name: string
  readonly userExam: MilestoneUserExam | null = null
  readonly userExamScore: number
  readonly hideResult: boolean = false
  readonly image: Image | undefined
  constructor(data: { [key: string]: any }) {
    this.id = data.id
    this.name = data.name
    this.userExamScore = data.userExamScore
    this.hideResult = data.hideResult ?? false
    if (data.userExam) {
      this.userExam = new MilestoneUserExam(data.userExam)
    }
    if (data.image) {
      this.image = new Image(data.image)
    }
  }

  get hasUserExam(){
    return !!this.userExam
  }
}

class MilestoneCourse {
  readonly id: number
  readonly name: string
  readonly description: string
  readonly image: Image

  constructor(data: { [key: string]: any }) {
    this.id = data.id
    this.name = data.name
    this.description = data.description

    if (data.image) {
      this.image = new Image(data.image)
    }
  }
}

class MilestoneEnrollment {
  readonly id: number
  readonly course: MilestoneCourse
  readonly progressPercent: number

  constructor(data: { [key: string]: any }) {
    this.id = data.id

    if (data.course) {
      this.course = new MilestoneCourse(data.course)
    }
    this.progressPercent = data.progressPercent
  }
}

export class Milestone {
  readonly id: number
  readonly name: string
  readonly body: string
  readonly targetDate: string
  readonly startDate: string
  readonly completedAt: Date | null
  readonly enrollments: MilestoneEnrollment[] = []
  readonly exams: MilestoneExam[] = []

  constructor(data: { [key: string]: any }) {
    this.id = data.id
    this.name = data.name
    this.body = data.body
    this.targetDate = data.targetDate
    this.startDate = data.startDate
    this.completedAt = data.completedAt ? new Date(data.completedAt) : null

    if (data.enrollments) {
      this.enrollments = data.enrollments.map(item => new MilestoneEnrollment(item))
    }
    if (data.exams) {
      this.exams = data.exams.map(item => new MilestoneExam(item))
    }
  }

  get joinedExams(){
    return this.exams.filter(item => item.hasUserExam)
  }
}

export default class UserGoal {
  readonly id: number
  readonly progressRate: number
  readonly startDate: string
  readonly endDate: string
  readonly status: string
  readonly daysRemaining: number
  readonly goal: Goal
  readonly milestones: Milestone[] = []

  constructor(data: { [key: string]: any }) {
    if (!data) {
      throw new Error('UserGoal data cannot be null or undefined')
    }
    this.id = data.id
    this.progressRate = data.progressRate
    this.startDate = data.startDate
    this.endDate = data.endDate
    this.status = data.status
    this.daysRemaining = data.daysRemaining

    if (data.goal) {
      this.goal = new Goal(data.goal)
    }
    if (data.milestones) {
      this.milestones = data.milestones.map(item => new Milestone(item))
    }
  }

  get rangeDays(){
    return Math.abs(differenceInDays(
      this.startDate,
      this.endDate
    ))
  }

  get isOutSchedule(){
    return this.daysRemaining <= 0 && this.progressRate < 100
  }

  get isLate(){
    return this.isOutSchedule || (((this.daysRemaining / this.rangeDays) < 0.3) && this.progressRate < 50)
  }

  get isOnSchedule(){
    return !(this.isOutSchedule && this.isLate)
  }

  get joinedExams(){
    return this.milestones.map(item => item.joinedExams).flat()
  }

  get latestJoinedExams(){
    return this.joinedExams
  }
}
