import Attachment from "./attachment";

export default class Message {
  readonly id: number
  readonly userName: string
  readonly userId: string
  readonly userAvatar: string
  readonly content: string
  readonly reasoningContent: string
  readonly threadId: string
  readonly isBot: boolean
  readonly attachments: Attachment[]
  readonly contentRaw: string
  readonly agentId: number | null
  readonly created_at: string
  public speechPlaying: boolean = false
  public cachedSpeechUrl: string

  constructor(data: { [key: string]: any }) {
    this.id = data.id
    this.userName = data.userName
    this.userId = data.userId
    this.userAvatar = data.userAvatar
    this.content = data.content
    this.reasoningContent = data.reasoningContent
    this.isBot = data.isBot
    this.threadId = data.threadId
    this.attachments = data.attachments
    this.contentRaw = data.contentRaw
    this.agentId = data.agentId
    this.created_at = data.created_at || data.createdAt || new Date().toISOString()
  }
}
