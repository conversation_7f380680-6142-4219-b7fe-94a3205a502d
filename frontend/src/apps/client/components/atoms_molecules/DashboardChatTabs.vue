<template>
  <div class="dashboard-chat-tabs d-flex flex-column">
    <!-- Header with AI Assistant title -->
    <div class="chat-header p-3 border-bottom">
      <h3 class="gray--text mb-0 d-flex align-items-center">
        <i class="v-icon material-icons me-2">smart_toy</i>
        AIアシスタント
      </h3>
    </div>

    <!-- Tabs Navigation -->
    <v-tabs v-model="tab" class="flex-shrink-0">
      <v-tab key="CHAT" @click="scrollChatToBottom">チャット</v-tab>
      <v-tab key="THREAD">スレッド</v-tab>
      <div style="padding-right: 40px"></div>

      <v-btn 
        v-if="showCreateThreadBtn" 
        class="btn-new-chat" 
        :icon="true" 
        @click="emitCreateThread(0)"
      >
        <i class="v-icon material-icons theme--dark">add_comment</i>
      </v-btn>
    </v-tabs>

    <!-- Tabs Content -->
    <v-tabs-items v-model="tab" class="flex-grow-1">
      <!-- Chat Tab -->
      <v-tab-item key="CHAT">
        <MesageContainer
          ref="mesageContainer"
          :key="`dashboard_${targetableId}`"
          :targetableId="targetableId"
          :targetableType="targetableType"
          :threadId="threadId"
          :toggleCreateThreadBtn="toggleCreateThreadBtn"
          :showComponent="true"
        />
      </v-tab-item>

      <!-- Thread Tab -->
      <v-tab-item key="THREAD">
        <ThreadContainer
          ref="threadContainer"
          :key="`dashboard_thread_${targetableId}`"
          :targetableId="targetableId"
          :targetableType="targetableType"
          :parentTab="1"
          @updateParentTab="updateParentTab"
          class="h-100"
        />
      </v-tab-item>
    </v-tabs-items>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Component, Prop } from 'vue-property-decorator'
import MesageContainer from '../organisms/MesageContainer.vue'
import ThreadContainer from '../organisms/ThreadContainer.vue'

@Component({
  components: {
    MesageContainer,
    ThreadContainer
  }
})
export default class DashboardChatTabs extends Vue {
  @Prop({ default: 'School' }) targetableType!: string
  @Prop({ required: true }) targetableId!: string
  @Prop({ default: '' }) threadId!: string

  tab: number = 0
  showCreateThreadBtn: boolean = false

  mounted() {
    this.tab = 0
  }

  toggleCreateThreadBtn(show: boolean) {
    this.showCreateThreadBtn = show
  }

  scrollChatToBottom() {
    this.$nextTick(() => {
      const mesageContainer = this.$refs.mesageContainer as any
      if (mesageContainer && mesageContainer.scrollToBottom) {
        mesageContainer.scrollToBottom()
      }
    })
  }

  emitCreateThread(_parentTab: number) {
    const mesageContainer = this.$refs.mesageContainer as any
    if (mesageContainer && mesageContainer.createThread) {
      mesageContainer.createThread()
    }

    this.$nextTick(() => {
      this.tab = 0
    })

    this.showCreateThreadBtn = false
  }

  updateParentTab(tabIndex: number) {
    this.tab = tabIndex - 1
  }
}
</script>

<style scoped>
.dashboard-chat-tabs {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.chat-header {
  flex-shrink: 0;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.btn-new-chat {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: #007bff !important;
  color: white !important;
  width: 32px;
  height: 32px;
  min-width: 32px;
}

.btn-new-chat:hover {
  background: #0056b3 !important;
}

/* Tab styling */
.v-tabs {
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.v-tab {
  font-weight: 500;
  text-transform: none;
  letter-spacing: normal;
}

.v-tab--active {
  color: #007bff !important;
}

.v-tabs-items {
  background: transparent;
}

.v-tab-item {
  padding: 0;
}

.h-100 {
  height: 100%;
}

.flex-grow-1 {
  flex-grow: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}
</style>
