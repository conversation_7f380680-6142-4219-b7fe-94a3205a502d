<template>
    <canvas ref="chart"></canvas>
</template>

<script lang="ts">
import Vue from 'vue'
import { Component, Prop, Watch } from 'vue-property-decorator'
import { Chart, registerables } from 'chart.js'
Chart.register(...registerables)

@Component({})
export default class ChartBar extends Vue {
  @Prop()
  data: any

  @Prop()
  chartOptions: any

  chart: any | null

  mounted() {
  }

  get defaultOptions() {
    return {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          type: 'category',
          display: true
        },
        y: {
          type: 'linear',
          display: true,
          beginAtZero: true
        }
      }
    }
  }

  get mergedOptions() {
    return {
      ...this.defaultOptions,
      ...this.chartOptions
    }
  }

  @Watch('data', {deep: true})
  dataChange(){
    if (!this.data || !this.data.datasets) {
      return
    }

    if (this.chart) {
      this.chart.data = this.data
      this.chart.update()
    } else {
      this.chart = new Chart(
        this.$refs['chart'] as any,
        {
          type: 'bar',
          data: this.data,
          options: this.mergedOptions
        }
      );
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
