<template>
  <SubPage :pageMode="pageMode" :onSwitchMode="onSwitchMode">
    <template #main_page>
      <div fluid class="dashboard-container container-fix-padding">
        <div class="d-flex h-100" :class="mobile ? 'flex-column' : 'flex-row'">
          <div class="dashboard-container-item p-0 dashboard-content-scroll" :class="mobile ? 'w-100' : 'col-8'">
            <template v-if="getBanner('user_dashboard_top')">
              <div v-html="getBanner('user_dashboard_top').bodyHtml"></div>
            </template>
            <div class="bg-white p-2">
              <div v-if="mobile">
                <div class="d-flex align-center">
                  <DashboardProgress class="w-50" :progressRate="learnProgressRate" />
                  <div class="w-50">
                    <div class="d-flex align-center justify-space-between mb-2">
                      <div>
                        <div class="text-overline">今週の学習</div>
                        <div class="d-flex">
                          <div class="fw-bold">{{ learnTimeToTimeJp }}</div>
                        </div>
                      </div>
                      <UserTimeGoalChangeDialog :update="updateTimeGoal" />
                    </div>
                    <DashboardTimeSummary
                      class="d-flex align-end"
                      :height="70"
                      :width="100"
                      :timeGraphData="timeGraphData"
                    />
                  </div>
                </div>
                <div class="d-flex border-top border-bottom">
                  <div class="w-33 pe-2 py-2">
                    <div class="d-flex align-center">
                      <v-icon small>mdi-clock-time-five-outline</v-icon>
                      <span class="ms-1 text-caption">学習時間</span>
                    </div>
                    <div class="text-align-right">
                      <span :class="mobile ? 'text-h7' : 'text-h4'">{{ totalLearnTimeToTimeJp }}</span>
                    </div>
                  </div>
                  <div class="w-33 border-left px-2 py-2">
                    <div class="d-flex align-center">
                      <v-icon small>mdi-file-document-multiple-outline</v-icon>
                      <span class="ms-1 text-caption">Act数</span>
                    </div>
                    <div class="text-align-right">
                      <!-- 表示は computedActivityCount を利用 -->
                      <span class="text-h4">{{ computedActivityCount }}</span>
                      <span class="text-caption">回</span>
                    </div>
                  </div>
                  <div class="w-33 border-left ps-2 py-2">
                    <div class="d-flex align-center">
                      <v-icon small>mdi-file-document-multiple</v-icon>
                      <span class="ms-1 text-caption">{{ customTexts.get('classroom_dashboard.consecutive_days') || '連続日数' }}</span>
                    </div>
                    <div class="text-align-right">
                      <span class="text-h4">{{ consecutiveDayCount }}</span>
                      <span class="text-caption">日</span>
                    </div>
                  </div>
                </div>
                <div class="d-flex pt-2 justify-space-between align-center px-3" @click="toActivity">
                  <div class="d-flex">
                    <v-icon>mdi-file-chart-outline</v-icon>
                    <span class="text-overline ms-1">学習レポート</span>
                  </div>
                  <div>
                    >
                  </div>
                </div>
              </div>
              <div v-else class="d-flex">
                <div class="w-37" :style="{ minWidth: '320px' }">
                  <DashboardProgress :progressRate="learnProgressRate">
                    <div>{{ customTexts.get('classroom_dashboard.this_week_goal') || '今週目標' }}</div>
                    <div class="d-flex justify-space-between">
                      <div class="fw-bold">{{ learnTimeToTimeJp }}</div>
                      <UserTimeGoalChangeDialog :update="updateTimeGoal" />
                    </div>
                    <div class="mt-2">
                      <div>{{ customTexts.get('classroom_dashboard.this_week_learning') || '今週の学習' }}</div>
                      <div>
                        <div class="fw-bold">{{ totalLearnTimeToTimeJp }}</div>
                      </div>
                    </div>
                  </DashboardProgress>
                </div>
                <div class="w-32 border-left px-3">
                  <DashboardTimeSummary :timeGraphData="timeGraphData" />
                  <div class="d-flex flex-row justify-space-between align-center">
                    <div class="text-08rem">
                      {{ dateFormat(dateRange.startDate) }} 〜 {{ dateFormat(dateRange.endDate) }}
                    </div>
                    <div class="ms-3">
                      <v-btn x-small color="primary" dark @click="toActivity">
                        詳細見る
                      </v-btn>
                    </div>
                  </div>
                </div>
                <div class="w-30 border-left">
                  <div class="ps-3 h-50 d-flex justify-space-between flex-column pb-2">
                    <div class="d-flex align-center">
                      <v-icon small>mdi-file-document-multiple-outline</v-icon>
                      <span class="ms-1 text-caption">{{ customTexts.get('classroom_dashboard.total_completed_count') || '学習完了数' }}</span>
                    </div>
                    <div>
                      <!-- 表示は computedActivityCount を利用 -->
                      <span class="text-h4">{{ computedActivityCount }}</span>
                      <span class="text-caption">回</span>
                    </div>
                  </div>
                  <div class="ps-3 border-top h-50 pt-2 d-flex justify-space-between flex-column">
                    <div class="d-flex align-center">
                      <v-icon small>mdi-file-document-multiple</v-icon>
                      <span class="ms-1 text-caption">連続日数</span>
                    </div>
                    <div>
                      <span class="text-h4">{{ consecutiveDayCount }}</span>
                      <span class="text-caption">日</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 以下、ダッシュボード下部のその他コンテンツ -->
            <div v-if="user && user.showDashboardInformation" class="p-3 mt-3 bg-white">
              <div>
                <div class="d-flex justify-space-between">
                  <div class="text-x-large font-bold">{{ user.showDashboardInformation.title }}</div>
                  <div class="text-small ms-2 border-default p-1 text-blue cursor-pointer" @click="readInformation(user.showDashboardInformation)">
                    非表示にする
                  </div>
                </div>
                <v-divider class="mt-2" />
                <div class="bg-white px-2 py-1 mt-2 info-content">
                  <p class="my-3 html-body" v-html="user.showDashboardInformation.content"></p>
                </div>
              </div>
            </div>
            <div v-if="unreadInformations.length > 0" class="p-3 mt-3" style="background: #FFFFF0;">
              <div>
                <div class="d-flex justify-space-between">
                  <div class="text-large">お知らせ</div>
                  <div class="text-small">未読: {{ user.unreadInformationsCount }}件</div>
                </div>
                <div class="bg-white border-default px-2 py-1 mt-2">
                  <template v-for="(item, index) in latestiUnreadInformations">
                    <InformationView :item="item" :key="`item-${item.id}`" @click.native="toInfo(item)" />
                    <v-divider v-if="index < latestiUnreadInformations.length - 1" :key="`divider-${item.id}`" />
                  </template>
                </div>
                <div class="d-flex justify-end mt-1">
                  <router-link class="text-blue cursor-pointer" to="/classroom/infors">
                    すべてのお知らせを見る
                  </router-link>
                </div>
              </div>
            </div>
            <div class="bg-white p-3 mt-3" v-if="currentUserGoal">
              <h3 class="gray--text font-weight-bold">
                <v-icon class="me-1">mdi-bullseye-arrow</v-icon>{{ customTexts.get('classroom_dashboard.goal') || 'ゴール' }}
              </h3>
              <div class="row">
                <div class="col-md-3 col-sm-12">
                  <v-img :src="currentUserGoal.goal.image.url"></v-img>
                </div>
                <div class="col-md-6 col-sm-12">
                  <div class="text-h6" @click="goToUserGoal(currentUserGoal)" style="cursor: pointer;">
                    {{ currentUserGoal.goal.name }}
                  </div>
                  <div class="mt-2 mb-1">
                    目標期間 {{ dateFormat(userGoal.startDate, 'YYYY/MM/DD') }}～{{ dateFormat(userGoal.endDate, 'YYYY/MM/DD') }}
                  </div>
                  <div class="d-flex align-items-center">
                    <div>
                      <v-chip color="red" text-color="white" v-if="userGoal.isOutSchedule" :small="mobile">
                        遅れてます
                      </v-chip>
                      <v-chip v-else-if="userGoal.isLate" color="green" text-color="white" :small="mobile">
                        やや遅れています
                      </v-chip>
                      <v-chip v-else-if="userGoal.isOnSchedule" color="primary" :small="mobile">
                        順調
                      </v-chip>
                    </div>
                    <div class="font-bold ms-2">
                      残り日数{{ userGoal.daysRemaining }}日
                    </div>
                  </div>
                </div>
                <div class="col-md-3 col-sm-12">
                  <div class="d-flex flex-row align-self-center justify-content-center">
                    <v-progress-circular :rotate="270" :size="150" :width="20" :value="userGoal.progressRate" color="primary">
                      <div style="line-height: 1;text-align: center;">
                        <span style="font-size: xx-large;">{{ userGoal.progressRate }} </span>%
                      </div>
                    </v-progress-circular>
                  </div>
                  <div class="mt-2 text-center">{{ customTexts.get('classroom_dashboard.progress_rate') || '進捗率' }}</div>
                  <div v-if="totalUserGoalsCount() > 0" class="mt-2 text-center text-blue cursor-pointer" @click="goToUserGoals">
                    ゴール他{{ totalUserGoalsCount() }}つを全部見る
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-white p-3 mt-3">
              <div>
                <template v-if="firstEnrollments.length > 0">
                  <h3 class="gray--text">{{ customTexts.get('classroom_dashboard.class_in_progress') || '授業中の講座' }}</h3>
                  <template v-for="enrollment in firstEnrollments">
                    <v-divider :key="`divider-${enrollment.id}`" />
                    <CourseMy :enrollment="enrollment" :key="enrollment.id" />
                  </template>
                  <template v-if="isMoreCourse">
                    <div class="d-flex justify-end mt-4 me-4">
                      <router-link class="show-more" to="/classroom/my-courses">もっと見る</router-link>
                    </div>
                  </template>
                </template>
                <template v-else>
                  <h3 class="gray--text">まだ受講中の講座はありません</h3>
                  <a class="exams-list" href="/courses">講座一覧を見る</a>
                </template>
              </div>
              <div class="mt-5">
                <template v-if="exams.length > 0">
                  <h3 class="gray--text">{{ customTexts.get('classroom_dashboard.recent_tests') || '最近受けたテスト' }}</h3>
                  <div v-for="(exam, index) in exams.slice(0, 3)" :key="index">
                    <Exam
                      :exam="exam"
                      :isMine="true"
                      :schoolSetting="schoolSetting"
                      :onShowHistories="onShowHistories"
                      @fetchList="refetchList"
                    />
                  </div>
                  <template v-if="isMoreExam">
                    <div class="d-flex justify-end mt-3 me-4">
                      <router-link class="show-more" to="/classroom/my-exams">もっと見る</router-link>
                    </div>
                  </template>
                </template>
                <template v-else>
                  <h3 class="gray--text">{{ customTexts.get('classroom_dashboard.no_test_results') || 'まだテスト結果はありません' }} {{ exams.length }}</h3>
                  <a class="exams-list" href="/exams">テスト一覧を見る</a>
                </template>
              </div>
            </div>

            <!-- AI Dashboard Chat - Mobile -->
            <div v-if="mobile" class="mt-3" style="height: 500px;">
              <DashboardChatTabs
                :targetableType="'School'"
                :targetableId="schoolId.toString()"
                :threadId="dashboardChatThreadId"
              />
            </div>

            <div class="col-sm-2" v-if="getBanner('user_dashboard_bottom')">
              <div v-html="getBanner('user_dashboard_bottom').bodyHtml"></div>
            </div>
          </div>

          <!-- AI Dashboard Chat - Right Side -->
          <div class="col-4 p-0 ps-3 dashboard-chat-container" v-if="!mobile">
            <DashboardChatTabs
              :targetableType="'School'"
              :targetableId="schoolId.toString()"
              :threadId="dashboardChatThreadId"
            />
          </div>

          <div class="col-sm-2" v-if="getBanner('user_dashboard_sidebar_top')">
            <div v-html="getBanner('user_dashboard_sidebar_top').bodyHtml"></div>
          </div>
        </div>
      </div>
    </template>
    <template #sub_page>
      <Exam
        v-if="subPageExam"
        :exam="subPageExam"
        :isMine="true"
        :schoolSetting="schoolSetting"
        @fetchList="refetchList"
      />
    </template>
  </SubPage>
</template>

<script lang="ts">
import Vue from 'vue'
import { Component } from 'vue-property-decorator'
import CourseList from '../pages/CourseList.vue'
import DashboardChart from './DashboardChart.vue'
import SubPage from '../atoms_molecules/SubPage.vue'
import { headerState } from '../../stores/header'
import { footerState } from '../../stores/footer'
import Banner from '../../../api/v2/models/banner'
import Exam from '../organisms/Exam.vue'
import Enrollment from '../../../api/v2/models/enrollment'
import UserGoal from '../../../api/v2/models/user_goal'
import CourseMy from '../atoms_molecules/CourseMy.vue'
import DashboardProgress from '../atoms_molecules/DashboardProgress.vue'
import InformationView from '../atoms_molecules/InformationView.vue'
import DashboardTimeSummary from '../atoms_molecules/DashboardTimeSummary.vue'
import UserTimeGoalChangeDialog from '../atoms_molecules/UserTimeGoalChangeDialog.vue'
import DashboardChatTabs from '../atoms_molecules/DashboardChatTabs.vue'
import api from '../../../api/v2/api'
import { subWeeks, getISODay, format, addDays } from 'date-fns'
import UserTrackingData from '../../../api/v2/models/user_tracking_data'
import { userState } from '../../stores/user'
import { checkMobile } from '../../utils/utils'
import { ROUTER_NAME } from '../../router'
import { sortedUniq, sum, round, max, min } from 'lodash'
import Information from '../../../api/v2/models/information'
import ExamModel from '../../../api/v2/models/exam'
import { convertMinutesToTimeJp } from '../../../../lib/convert'
import { examMyState } from '../../stores/exam_my'
import { PageModeType } from '../../types'

@Component({
  components: {
    CourseList,
    DashboardChart,
    Exam,
    CourseMy,
    DashboardProgress,
    DashboardTimeSummary,
    UserTimeGoalChangeDialog,
    InformationView,
    SubPage,
    DashboardChatTabs
  }
})
export default class Dashboard extends Vue {
  mobile = checkMobile()

  banners: Banner[] = []

  get user() {
    return userState.user
  }

  get schoolSetting() {
    return headerState.schoolSetting
  }

  get customTexts() {
    return headerState.customTexts
  }

  exams: ExamModel[] = []

  get isMoreExam() {
    return this.exams.length > 3
  }

  enrollments: Enrollment[] = []
  get firstEnrollments() {
    return this.enrollments.slice(0, 3)
  }

  userGoal: UserGoal | null = null
  get currentUserGoal() {
    return this.userGoal
  }

  userGoalsCount: number = 0
  totalUserGoalsCount() {
    return this.userGoalsCount - 1 > 0 ? this.userGoalsCount - 1 : 0
  }

  get isMoreCourse() {
    return this.firstEnrollments.length < this.enrollments.length
  }

  get unreadInformations() {
    return this.user ? this.user.unreadInformations : []
  }
  get latestiUnreadInformations() {
    return this.user ? this.user.unreadInformations.slice(0, 3) : []
  }

  get learnTimeGoalMinutes() {
    return userState.user ? userState.user.learnTimeGoalMinutes : 0
  }

  get learnTimeToTimeJp() {
    return convertMinutesToTimeJp(this.learnTimeGoalMinutes)
  }

  // トラッキングデータ（api.tracking.getで取得）
  userTrackingData: UserTrackingData | null = null
  // アクティビティデータ（api.activity.getで取得）→ 学習完了数計算用
  activitiesData: any[] = []
  dateRange = {
    startDate: addDays(subWeeks(new Date(), 1), 1),
    endDate: new Date()
  }

  get timeGraphData() {
    if (!this.userTrackingData) {
      return null
    }
    return {
      labels: this.userTrackingData.labels.map((date) => {
        switch (getISODay(new Date(date))) {
          case 1:
            return '月'
          case 2:
            return '火'
          case 3:
            return '水'
          case 4:
            return '木'
          case 5:
            return '金'
          case 6:
            return '土'
          case 7:
            return '日'
        }
      }),
      datasets: [
        {
          label: '学習時間',
          backgroundColor: '#44a2eb',
          fill: false,
          data: this.userTrackingData.trackingUserValues
        }
      ]
    }
  }
  
  // DashboardChart.vue と同じロジックで、各日の学習完了数を算出する
  get computedActivityCount() {
    if (!this.userTrackingData || !this.userTrackingData.labels) {
      return 0
    }
    const dailyCounts = this.userTrackingData.labels.map((label: string) => {
      const dateStr = format(new Date(label), 'yyyy-MM-dd')
      const activitiesForDate = this.activitiesData.filter((act: any) => {
        const actDateStr = format(new Date(act.createdAt), 'yyyy-MM-dd')
        return actDateStr === dateStr
      })
      return activitiesForDate.length
    })
    return sum(dailyCounts)
  }

  // その結果を activityCount として利用
  get activityCount() {
    return this.computedActivityCount
  }

  get totalLearnTimeMinutes() {
    if (!this.userTrackingData) {
      return 0
    }
    return sum(this.userTrackingData.trackingUserValues)
  }

  get totalLearnTimeToTimeJp() {
    return convertMinutesToTimeJp(this.totalLearnTimeMinutes)
  }

  get learnProgressRate() {
    if (!this.learnTimeGoalMinutes) {
      return 0
    }
    return Math.round((this.totalLearnTimeMinutes / this.learnTimeGoalMinutes) * 100)
  }

  get consecutiveDayCount() {
    if (!this.userTrackingData) {
      return 0
    }
    return this.userTrackingData.consecutiveDayCount
  }

  get totalTime() {
    if (!this.userTrackingData) {
      return 0
    }
    let total = 0
    this.userTrackingData.trackingUserValues.forEach((value) => {
      total += value
    })
    return total
  }

  async updateTimeGoal(learnTimeGoalValue: number) {
    await userState.updateLearnTimeGoalValue({ learnTimeGoalValue })
  }

  pageMode: PageModeType = 'main_page'
  subPageExam: ExamModel | null = null
  onSwitchMode(pageMode: PageModeType) {
    this.pageMode = pageMode
    this.$router.push({
      query: {
        ...(this.$route.query ?? {}),
        exam_id: undefined
      }
    })
  }

  onShowHistories(exam: ExamModel) {
    this.pageMode = 'sub_page'
    this.subPageExam = exam
    this.$router.push({
      query: {
        ...(this.$route.query ?? {}),
        exam_id: exam.id.toString()
      }
    })
  }

  refetchList() {
    examMyState.getMyExams().then(() => {
      this.exams = examMyState.list
      if (this.$route.query['exam_id']) {
        const found = this.exams.find((item) => item.id.toString() === this.$route.query['exam_id'])
        if (found) {
          this.onShowHistories(found)
        }
      }
      if (this.subPageExam) {
        const found = this.exams.find((item) => item.id === this.subPageExam.id)
        if (found) {
          this.subPageExam = found
        }
      }
    })
  }

  created() {
    headerState.setTitle('ダッシュボード')
    footerState.setFooterDefault()
    this.refetchList()
    api.enrollment
      .index()
      .then((enrollments) => {
        this.enrollments = enrollments
      })
      .catch()
    api.banner
      .index({ type: 'user_dashboard' })
      .then((banners) => {
        this.banners = banners
      })
      .catch()
    // トラッキングデータの取得
    api.tracking
      .get(this.dateRange)
      .then((res) => {
        this.userTrackingData = res
      })
      .catch()
    // アクティビティデータの取得（学習完了数計算用）
    api.activity
      .get(0, this.dateRange)
      .then((res) => {
        this.activitiesData = res.activities || []
      })
      .catch()
    api.userGoal.currentGoal()
      .then((userGoal) => {
        this.userGoal = userGoal
      })
      .catch((e) => {
        console.log(e)
      })
    api.userGoal.index()
      .then((userGoals) => {
        this.userGoalsCount = userGoals.length
      })
      .catch((e) => {
        console.log(e)
      })
    userState.getUser()
  }

  getBanner(position) {
    return this.banners.find((banner) => banner.position === position)
  }

  get getSideBarBanner() {
    const banner = this.getBanner('user_dashboard_sidebar_top')
    return banner ? banner.bodyHtml : ''
  }

  dateFormat(date: any, formatDate: string = 'YYYY年MM月DD日') {
    return format(date, formatDate)
  }

  toActivity() {
    this.$router.push({
      name: ROUTER_NAME.ACTIVITY_LIST
    })
  }

  toInfo(info: Information) {
    this.$router.push({
      name: ROUTER_NAME.INFOR_DETAIL,
      params: { inforId: info.id.toString() }
    })
  }

  readInformation(info: Information) {
    api.infor.show(info.id.toString()).then(() => {
      userState.getUser()
    })
  }

  goToUserGoal(userGoal: UserGoal) {
    this.$router.push({
      name: ROUTER_NAME.USER_GOAL_DETAIL,
      params: { userGoalId: userGoal.id.toString() }
    })
  }

  goToUserGoals() {
    this.$router.push({ name: ROUTER_NAME.USER_GOAL_LIST })
  }

  // Computed properties for DashboardChat
  get schoolId(): number {
    return userState.user?.currentSchoolId || 0
  }

  get dashboardChatThreadId(): string {
    // Generate a unique thread ID for dashboard chat
    return `dashboard_${userState.user?.id || 0}_${this.schoolId}`
  }
}
</script>

<style lang="scss">
.dashboard-container {
  background: #eee;
}

#chat-container  {
  height: calc(100vh - 330px) !important;
}

#thread-container {
  height: calc(100vh - 190px) !important;
}

.dashboard-content-scroll {
  height: calc(100vh - 60px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* Custom scrollbar for dashboard content */
.dashboard-content-scroll::-webkit-scrollbar {
  width: 6px;
}

.dashboard-content-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dashboard-content-scroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dashboard-content-scroll::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dashboard-chat-container {
  height: calc(100vh - 275px);
}


@media (max-width: 768px) {
  .dashboard-content-scroll {
    height: auto;
    overflow: visible;
  }

  .dashboard-container {
    height: auto;
  }
}
</style>
