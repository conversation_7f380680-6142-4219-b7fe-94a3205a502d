<template>
  <div v-if="showComponent" flat class="bg-white d-flex flex-column justify-content-between position-relative"
       @dragover="!usingDeepseek ? dragover($event) : undefined"
       @dragleave="!usingDeepseek ? dragleave() : undefined"
       @drop="!usingDeepseek ? drop($event) : undefined">
    <div class="chat-container flex-grow-1 d-flex flex-column" id="chat-container" ref="chatContainer">
      <audio ref="speechMedia" style="display: none"></audio>
      <div>
        <template v-if="messages.length === 0">
          <div class="suggestion-question-list">
            <div class="d-flex justify-center pb-3">
              <img :src="logo" alt="logo" style="width: 100px; border: unset;">
            </div>
            <div class="d-flex justify-center" v-if="aiChatQuestions.length > 0">
              <div v-if="aiChatQuestions[0]" class="suggestion-question" @click="sendSuggestQuestion(aiChatQuestions[0])">
                <div class="suggestion-question-icon">
                  <img v-if="aiChatQuestions[0].icon" :src="aiChatQuestions[0].icon">
                  <i v-else class="v-icon material-icons">rate_review</i>
                </div>
                <div class="suggestion-question-title">{{aiChatQuestions[0].title}}</div>
              </div>
              <div v-if="aiChatQuestions[1]" class="suggestion-question" @click="sendSuggestQuestion(aiChatQuestions[1])">
                <div class="suggestion-question-icon">
                  <img v-if="aiChatQuestions[1].icon" :src="aiChatQuestions[1].icon">
                  <i v-else class="v-icon material-icons">rate_review</i>
                </div>
                <div class="suggestion-question-title">{{aiChatQuestions[1].title}}</div>
              </div>
            </div>
            <div class="d-flex justify-center" v-if="aiChatQuestions.length > 2">
              <div v-if="aiChatQuestions[2]" class="suggestion-question" @click="sendSuggestQuestion(aiChatQuestions[2])">
                <div class="suggestion-question-icon">
                  <img v-if="aiChatQuestions[2].icon" :src="aiChatQuestions[2].icon">
                  <i v-else class="v-icon material-icons">rate_review</i>
                </div>
                <div class="suggestion-question-title">{{aiChatQuestions[2].title}}</div>
              </div>
              <div v-if="aiChatQuestions[3]" class="suggestion-question" @click="sendSuggestQuestion(aiChatQuestions[3])">
                <div class="suggestion-question-icon">
                  <img v-if="aiChatQuestions[3].icon" :src="aiChatQuestions[3].icon">
                  <i v-else class="v-icon material-icons">rate_review</i>
                </div>
                <div class="suggestion-question-title">{{aiChatQuestions[3].title}}</div>
              </div>
            </div>
          </div>
        </template>

        <template v-else v-for="(message, messageIndex) in messages">
          <template v-if="message.isBot">
            <div class="chat incoming" :key="message.id">
              <div class="chat-content">
                <div class="chat-details">
                  <img :src="getAgentAvatar(message)" alt="chatbot-img"/>
                  <div :class="`message-content`">
                    <v-expansion-panels accordion class="mb-2" :id="`message-reasoning-container-${message.id}`" v-show="message.reasoningContent">
                      <v-expansion-panel>
                        <v-expansion-panel-header>
                          <div class="d-flex align-items-end gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="width: 16px; height: 16px; padding-right: 4px;">
                              <path d="M184 0c30.9 0 56 25.1 56 56l0 400c0 30.9-25.1 56-56 56c-28.9 0-52.7-21.9-55.7-50.1c-5.2 1.4-10.7 2.1-16.3 2.1c-35.3 0-64-28.7-64-64c0-7.4 1.3-14.6 3.6-21.2C21.4 367.4 0 338.2 0 304c0-31.9 18.7-59.5 45.8-72.3C37.1 220.8 32 207 32 192c0-30.7 21.6-56.3 50.4-62.6C80.8 123.9 80 118 80 112c0-29.9 20.6-55.1 48.3-62.1C131.3 21.9 155.1 0 184 0zM328 0c28.9 0 52.6 21.9 55.7 49.9c27.8 7 48.3 32.1 48.3 62.1c0 6-.8 11.9-2.4 17.4c28.8 6.2 50.4 31.9 50.4 62.6c0 15-5.1 28.8-13.8 39.7C493.3 244.5 512 272.1 512 304c0 34.2-21.4 63.4-51.6 74.8c2.3 6.6 3.6 13.8 3.6 21.2c0 35.3-28.7 64-64 64c-5.6 0-11.1-.7-16.3-2.1c-3 28.2-26.8 50.1-55.7 50.1c-30.9 0-56-25.1-56-56l0-400c0-30.9 25.1-56 56-56z"/>
                            </svg>
                            思考過程を表示
                            <div class="streaming-indicator" style="display: none">●</div>
                          </div>
                        </v-expansion-panel-header>
                        <v-expansion-panel-content>
                          <div :id="`message-reasoning-content-${message.id}`">{{message.reasoningContent}}</div>
                        </v-expansion-panel-content>
                      </v-expansion-panel>
                    </v-expansion-panels>
                    <div v-if="!message.content" class="typing-animation">
                      <div class="typing-dot dot1"></div>
                      <div class="typing-dot dot2"></div>
                      <div class="typing-dot dot3"></div>
                    </div>
                    <div :id="`message-content-${message.id}`" v-html="message.content"></div>
                    <div v-if="message.attachments.length" class="chat-input-attachments d-flex flex-column gap-1 mb-2">
                      <template v-for="(attachment, index) in message.attachments">
                        <img v-if="['PNG', 'JPG', 'JPEG'].includes(getFileExtFromString(attachment.filename))" :key="index" :src="attachment.url" alt="image" class="message-attachment-image">


                        <div v-else :key="index" class="chat-input-attachment-container">
                          <div class="chat-input-attachment d-flex" @click="openUrl(attachment.url)">
                            <div class="pr-2">
                              <FileIcon :fileName="attachment.filename" />
                            </div>

                            <div class="d-flex flex-column">
                              <div class="font-weight-bold chat-input-attachment-title">
                                {{ getFileNameFromString(attachment.filename) }}
                              </div>

                              <div class="chat-input-attachment-ext">{{ getFileExtFromString(attachment.filename) }}</div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                    <div :id="`message-${message.id}-actions`" class="message-actions" v-show="message.contentRaw">
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn plain :icon="true" @click="speech(messageIndex)" small v-bind="attrs" v-on="on" v-show="!message.speechPlaying">
                            <i class="v-icon material-icons">volume_up</i>
                          </v-btn>
                        </template>
                        <span>Read Aloud</span>
                      </v-tooltip>

                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn plain :icon="true" @click="stopStream" small v-bind="attrs" v-on="on" v-show="message.speechPlaying" style="color: #343541">
                            <i class="v-icon material-icons">stop_circle</i>
                          </v-btn>
                        </template>
                        <span>Stop</span>
                      </v-tooltip>

                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn plain :icon="true" @click="copyMessage(messageIndex)" small v-bind="attrs" v-on="on">
                            <i class="v-icon material-icons">content_copy</i>
                          </v-btn>
                        </template>
                        <span>Copy</span>
                      </v-tooltip>

                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn plain :icon="true" @click="regenerate(messageIndex)" small v-show="messageIndex + 1 === messages.length" v-bind="attrs" v-on="on">
                            <i class="v-icon material-icons">cached</i>
                          </v-btn>
                        </template>
                        <span>Regenerate</span>
                      </v-tooltip>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="chat outgoing" :key="message.id">
              <div class="chat-content">
                <div class="chat-details">
                  <img :src="message.userAvatar" alt="user-img"/>
                  <div :class="`message-content message-content-${message.id}`">
                    {{ message.content }}

                    <div v-if="message.attachments.length" class="chat-input-attachments d-flex flex-column gap-1 mb-2">
                      <template v-for="(attachment, index) in message.attachments">
                        <img v-if="['PNG', 'JPG', 'JPEG'].includes(getFileExtFromString(attachment.filename))" :key="index" :src="attachment.url" alt="image" class="message-attachment-image">


                        <div v-else :key="index" class="chat-input-attachment-container">
                          <div class="chat-input-attachment d-flex" @click="openUrl(attachment.url)">
                            <div class="pr-2">
                              <FileIcon :fileName="attachment.filename" />
                            </div>

                            <div class="d-flex flex-column">
                              <div class="font-weight-bold chat-input-attachment-title">
                                {{ getFileNameFromString(attachment.filename) }}
                              </div>

                              <div class="chat-input-attachment-ext">{{ getFileExtFromString(attachment.filename) }}</div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </template>
      </div>
<!--      <div ref="last-message"/>-->
      <div id="last-message" style="margin-top: 10px"/>
    </div>

    <div id="agent-selector-container" class="d-flex justify-end pb-2 pt-1">
      <span v-if="availableAgents.length > 0 && messageCount < limitMessage" class="agent-selector-container">
        <select
          v-model="selectedAgentId"
          class="form-select form-select-sm"
          style="min-width: 200px; border-radius: 10px;">
          <option
            v-for="agent in availableAgents"
            :key="agent.id"
            :value="agent.id">
            {{ agent.name }}
          </option>
        </select>
      </span>
    </div>

    <div id="message-input-section" class="d-flex justify-end ps-1 pe-1">
      <span v-if="!hasAvailableAgent" class="limit-message limit-reached" style="color: #ff6b6b;">
        エージェントが設定されていません。管理者にお問い合わせください。
      </span>
      <span v-else-if="messageCount < limitMessage" class="limit-message">
        １日に{{ limitMessage }}回まで{{ messageCount }}/{{ limitMessage }}
      </span>
      <span v-else class="limit-message limit-reached">
        回数制限に達しました。{{ nextDayFormatted }} 0:00にリセットされます
      </span>
    </div>

    <div class="w-full p-3">
      <div class="chat-input-container d-flex" :class="{ 'disabled': messageCount >= limitMessage || !hasAvailableAgent }">
        <div class="d-flex align-end">
          <v-btn v-if="!usingDeepseek" plain :icon="true" @click="addAttachment()" :disabled="inProgress || messageCount >= limitMessage || !hasAvailableAgent">
            <i class="v-icon material-icons theme&#45;&#45;dark">attach_file</i>
          </v-btn>
        </div>
        <div :class="`d-flex flex-column w-full justify-center ${ attachments.length ? 'gap-1' : '' }`">
          <div class="chat-input-attachments d-flex flex-column gap-2">
            <template v-for="(attachment, index) in attachments">
              <div :class="`chat-input-attachment-container ${attachment.isDeleted ? 'd-none' : ''}`">
                <div :key="index" class="chat-input-attachment d-flex">
                  <div class="pr-2">
                    <FileIcon :fileName="attachment.file.name" />
                  </div>

                  <div class="d-flex flex-column">
                    <div class="font-weight-bold chat-input-attachment-title">
                      {{ getFileName(attachment) }}
                    </div>
                    <div class="chat-input-attachment-ext">{{ getFileExt(attachment) }}</div>
                  </div>
                </div>

                <div
                  class="chat-input-progress-bar"
                  :style="{
                    width: `${attachment.progressPercentage}%`,
                    display: `${attachment.isUploadCompleted ? 'none' : 'block'
                  }`}"
                ></div>
                <div class="chat-input-remove-icon">
                  <v-btn v-show="!inProgress && messageCount < limitMessage && hasAvailableAgent" outlined :icon="true" @click="deleteAttachment(index)">
                    <i class="v-icon material-icons theme&#45;&#45;dark">close</i>
                  </v-btn>
                </div>
              </div>
            </template>
          </div>

          <div class="d-flex flex-column justify-end">
            <textarea
              ref="chatInput"
              v-model="chatContent"
              id="chat-input"
              :placeholder="hasAvailableAgent ? '内容を入力してください' : 'エージェントが設定されていません'"
              rows="1"
              v-on:keydown="handleEnterChatForm"
              :disabled="inProgress || messageCount >= limitMessage || !hasAvailableAgent">
            </textarea>
          </div>
        </div>
        <div class="d-flex align-end">
          <v-btn v-show="inProgress" :icon="true" @click="stopGenerate()">
            <i class="v-icon material-icons theme--dark">stop_circle</i>
          </v-btn>
          <v-btn
            v-show="!inProgress"
            plain
            :disabled="!canSubmit() || messageCount >= limitMessage || !hasAvailableAgent"
            :icon="true"
            @click="createMessage()">
            <i class="v-icon material-icons theme--dark">send</i>
          </v-btn>
        </div>
      </div>
      <input ref="file" type="file" multiple class="hidden" v-show="false" v-on:change="handleFileChange" accept=".jpg, .jpeg, .png">
    </div>
    <div class="chat-input-backdrop" v-show="isDragging && !usingDeepseek">
      <div class="d-flex align-center">
        <div class="d-flex flex-column justify-center align-center chat-input-drag-message">
          <div class="mb-3">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 132 108" fill="none" width="132" height="108"><g clip-path="url(#clip0_3605_64419)"><path fill-rule="evenodd" clip-rule="evenodd" d="M25.2025 29.3514C10.778 33.2165 8.51524 37.1357 11.8281 49.4995L13.4846 55.6814C16.7975 68.0453 20.7166 70.308 35.1411 66.443L43.3837 64.2344C57.8082 60.3694 60.0709 56.4502 56.758 44.0864L55.1016 37.9044C51.7887 25.5406 47.8695 23.2778 33.445 27.1428L29.3237 28.2471L25.2025 29.3514ZM18.1944 42.7244C18.8572 41.5764 20.325 41.1831 21.4729 41.8459L27.3517 45.24C28.4996 45.9027 28.8929 47.3706 28.2301 48.5185L24.836 54.3972C24.1733 55.5451 22.7054 55.9384 21.5575 55.2757C20.4096 54.613 20.0163 53.1451 20.6791 51.9972L22.8732 48.1969L19.0729 46.0028C17.925 45.3401 17.5317 43.8723 18.1944 42.7244ZM29.4091 56.3843C29.066 55.104 29.8258 53.7879 31.1062 53.4449L40.3791 50.9602C41.6594 50.6172 42.9754 51.377 43.3184 52.6573C43.6615 53.9376 42.9017 55.2536 41.6214 55.5967L32.3485 58.0813C31.0682 58.4244 29.7522 57.6646 29.4091 56.3843Z" fill="#AFC1FF"></path></g><g clip-path="url(#clip1_3605_64419)"><path fill-rule="evenodd" clip-rule="evenodd" d="M86.8124 13.4036C81.0973 11.8722 78.5673 13.2649 77.0144 19.0603L68.7322 49.97C67.1793 55.7656 68.5935 58.2151 74.4696 59.7895L97.4908 65.958C103.367 67.5326 105.816 66.1184 107.406 60.1848L115.393 30.379C115.536 29.8456 115.217 29.2959 114.681 29.16C113.478 28.8544 112.435 28.6195 111.542 28.4183C106.243 27.2253 106.22 27.2201 109.449 20.7159C109.73 20.1507 109.426 19.4638 108.816 19.3004L86.8124 13.4036ZM87.2582 28.4311C86.234 28.1567 85.1812 28.7645 84.9067 29.7888C84.6323 30.813 85.2401 31.8658 86.2644 32.1403L101.101 36.1158C102.125 36.3902 103.178 35.7824 103.453 34.7581C103.727 33.7339 103.119 32.681 102.095 32.4066L87.2582 28.4311ZM82.9189 37.2074C83.1934 36.1831 84.2462 35.5753 85.2704 35.8497L100.107 39.8252C101.131 40.0996 101.739 41.1524 101.465 42.1767C101.19 43.201 100.137 43.8088 99.1132 43.5343L84.2766 39.5589C83.2523 39.2844 82.6445 38.2316 82.9189 37.2074ZM83.2826 43.2683C82.2584 42.9939 81.2056 43.6017 80.9311 44.626C80.6567 45.6502 81.2645 46.703 82.2888 46.9775L89.7071 48.9652C90.7313 49.2396 91.7841 48.6318 92.0586 47.6076C92.333 46.5833 91.7252 45.5305 90.7009 45.256L83.2826 43.2683Z" fill="#7989FF"></path></g><path fill-rule="evenodd" clip-rule="evenodd" d="M40.4004 71.8426C40.4004 57.2141 44.0575 53.5569 61.1242 53.5569H66.0004H70.8766C87.9432 53.5569 91.6004 57.2141 91.6004 71.8426V79.1569C91.6004 93.7855 87.9432 97.4426 70.8766 97.4426H61.1242C44.0575 97.4426 40.4004 93.7855 40.4004 79.1569V71.8426ZM78.8002 67.4995C78.8002 70.1504 76.6512 72.2995 74.0002 72.2995C71.3492 72.2995 69.2002 70.1504 69.2002 67.4995C69.2002 64.8485 71.3492 62.6995 74.0002 62.6995C76.6512 62.6995 78.8002 64.8485 78.8002 67.4995ZM60.7204 70.8597C60.2672 70.2553 59.5559 69.8997 58.8004 69.8997C58.045 69.8997 57.3337 70.2553 56.8804 70.8597L47.2804 83.6597C46.4851 84.72 46.7 86.2244 47.7604 87.0197C48.8208 87.8149 50.3251 87.6 51.1204 86.5397L58.8004 76.2997L66.4804 86.5397C66.8979 87.0962 67.5363 87.4443 68.2303 87.4936C68.9243 87.5429 69.6055 87.2887 70.0975 86.7967L74.8004 82.0938L79.5034 86.7967C80.4406 87.734 81.9602 87.734 82.8975 86.7967C83.8347 85.8595 83.8347 84.3399 82.8975 83.4026L76.4975 77.0026C75.5602 76.0653 74.0406 76.0653 73.1034 77.0026L68.6601 81.4459L60.7204 70.8597Z" fill="#3C46FF"></path><defs><clipPath id="clip0_3605_64419"><rect width="56" height="56" fill="white" transform="translate(0 26.9939) rotate(-15)"></rect></clipPath><clipPath id="clip1_3605_64419"><rect width="64" height="64" fill="white" transform="translate(69.5645 0.5) rotate(15)"></rect></clipPath></defs></svg>
          </div>

          <div>
            <h3>追加してみましょう</h3>
            <h4>会話に追加するファイルをここにドロップしてください</h4>
          </div>
        </div>
      </div>
    </div>

    <v-dialog v-model="codeDialog" max-width="800">
      <v-card>
        <v-card-text v-html="dialogHTML" class="d-flex pt-6">
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" @click="codeDialog = false">近い</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import {Component, Prop, Watch} from 'vue-property-decorator'
import Message from '../../../api/v2/models/message'
import api from '../../../api/v2/api'
import {makeChannel} from '../../../../action_cable/cable'
import MessageForm from '../../../api/v2/forms/message'
import ThreadForm from '../../../api/v2/forms/thread'
import FileIcon from '../atoms_molecules/FileIcon.vue'
import AttachmentForm from "../../../api/v2/forms/attachment";
import Axios from "axios";
import AiChatQuestion from "../../../api/v2/models/ai_chat_question";
import url_path from "../../../../helpers/url_helper";

const logo = require("../../../../images/edbase_logo.gif").default;

@Component({
  methods: {url_path},
  components: {
    FileIcon
  }
})
export default class MesageContainer extends Vue {
  @Prop({default: 'Lesson'}) targetableType: 'Lesson' | 'UserGoal' | 'Exam' | 'Question' | 'School'
  @Prop({required: true}) targetableId: string
  @Prop({required: false}) toggleCreateThreadBtn: (value: boolean) => undefined
  @Prop({default: false}) usingDeepseek: boolean
  @Prop({default: true}) showComponent: boolean

  messages: Message[] = []
  chatContent: string = ''
  threadId: string = ''
  limitMessage: number = 100
  messageCount: number = 0
  chanel: any = null
  attachments = []
  isDragging = false
  codeDialog = false
  dialogHTML = ""
  inProgress = false
  aiChatQuestions: AiChatQuestion[] = []
  disableScroll = false
  wheelDebounceTimer: any = null
  availableAgents: any[] = []
  selectedAgentId: number | null = null

  logo = logo

  get nextDayFormatted(): string {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    return `${tomorrow.getMonth() + 1}月${tomorrow.getDate()}日`;
  }

  get selectedAgent() {
    return this.availableAgents.find(agent => agent.id === this.selectedAgentId)
  }

  get hasAvailableAgent() {
    return this.availableAgents.length > 0 && this.selectedAgentId !== null
  }

  getAgentAvatar(message: any) {
    if (message.isBot && message.agentId) {
      const agent = this.availableAgents.find(agent => agent.id === message.agentId)
      return (agent && agent.imageUrl) || message.userAvatar
    }
    return message.userAvatar
  }

  createChannel() {
    this.chanel = makeChannel(
      'ChatLessonChannel',
      async (e) => {
        if (!e) return
        if (e.content == 'Finished') {
          this.inProgress = false
          setTimeout(() => document.getElementById('chat-input').focus(), 400)
          const message = await api.message.show(e.message_id)
          const messageIndex = this.messages.findIndex(m => m.id === e.message_id)
          if (messageIndex > -1) {
            this.messages.splice(messageIndex, 1, message)
          }
          this.scrollToLast()
        } else if (e.content == 'Stop finished') {
          this.inProgress = false
          setTimeout(() => document.getElementById('chat-input').focus(), 400)
        } else {
          ;(document.getElementById('chat-input') as HTMLInputElement).value = ''
          if (document.getElementsByClassName('typing-animation').length > 0) {
            document.getElementsByClassName('typing-animation')[0].remove()
          }

          if (e.content) {
            const messageEl = document.getElementById(`message-content-${e.message_id}`)
            if (messageEl) {
              messageEl.innerHTML = e.content
            }
          }

          if (e.reasoning_content) {
            const messageReasoningContainer = document.getElementById(`message-reasoning-container-${e.message_id}`)
            if (messageReasoningContainer)
              messageReasoningContainer.style.display = 'flex'

            const indicatorDot =  <HTMLElement> messageReasoningContainer.querySelector(".streaming-indicator")
            indicatorDot.style.display = e.content ? 'none' :'block'

            const messageReasoningEl = document.getElementById(`message-reasoning-content-${e.message_id}`)
            if (messageReasoningEl) {
              messageReasoningEl.innerText = e.reasoning_content
            }
          }

          this.scrollToLast()
        }
      },
      () => {
        this.chanel.send({
          action: 'follow',
          data: {
            targetable_id: this.targetableId,
            targetable_type: this.targetableType
          }
        })
      },
    )
  }

  reconnectChannel() {
    if (this.chanel) {
      this.chanel.unsubscribe()
      this.chanel = null
    }
    this.createChannel()
  }

  async createMessage(options = {
    aiChatQuestionId: undefined
  }) {
    this.disableScroll = false
    this.stopStream()
    try {
      this.inProgress = true
      let input = (document.getElementById('chat-input') as HTMLInputElement)
      let content = input.value
      const messageForm = new MessageForm()
      messageForm.content = content.trim()
      messageForm.targetableId = parseInt(this.targetableId)
      messageForm.targetableType = this.targetableType
      messageForm.attachmentIds = this.attachments.filter((attachment) =>
        !attachment.isDeleted && attachment.serverId
      ).map((attachment) => attachment.serverId)
      messageForm.aiChatQuestionId = options.aiChatQuestionId
      messageForm.agentId = this.selectedAgentId
      this.threadId = this.$route.query['thread_id'] ? String(this.$route.query['thread_id']) : this.threadId
      messageForm.threadId = this.threadId

      let message = await api.message.create(messageForm)
      if (this.threadId == '') {
        this.threadId = message.threadId
      }
      const result = await api.message.index({
        targetableId: this.targetableId,
        targetableType: this.targetableType,
        threadId: this.threadId
      })
      this.messages = result[1]
      if (this.toggleCreateThreadBtn) {
        this.toggleCreateThreadBtn(!!this.messages.length)
      }
      this.messageCount = result[0].totalMessage
      this.limitMessage = result[0].limitMessage
      this.chatContent = ''
      this.attachments = []
      setTimeout(() => input.focus(), 200)
      input.style.setProperty("height", "unset")
      this.scrollToLast()
    } catch (e) {
      console.error(e)
    } finally {
    }
  }

  @Watch('$route')
  async changeRoute() {
    this.stopStream()
    const threadId = this.$route.query['thread_id'] || ''
    this.threadId = threadId.toString()
    const result = await api.message.index({
      targetableId: this.targetableId,
      targetableType: this.targetableType,
      threadId: threadId.toString()
    })
    this.messages = result[1]
    if (this.toggleCreateThreadBtn) {
      this.toggleCreateThreadBtn(!!this.messages.length)
    }
    this.messageCount = result[0].totalMessage
    this.limitMessage = result[0].limitMessage
    this.chatContent = ''
    this.attachments = []
    this.scrollToLast()
  }

  scrollToLast() {
    if (this.disableScroll) return

    setTimeout(() => {
      let lastMessage = document.querySelector("#last-message") as HTMLElement
      if (lastMessage) {
        lastMessage.scrollIntoView({ behavior: 'smooth', block: 'end' })
      }
    }, 100)
  }

  async clearMessage() {
    try {
      ;[this.messages] = await Promise.all([
        api.message.clear({
          targetableId: this.targetableId,
          targetableType: this.targetableType
        })
      ])
    } catch (e) {}
  }

  async stopGenerate() {
    try {
      this.stopStream()

      ;[this.messages] = await Promise.all([
        api.message.stopGenerate({
          targetableId: this.targetableId,
          targetableType: this.targetableType
        })
      ])
    } catch (e) {}
  }

  async created() {
    try {
      const threadId = this.$route.query['thread_id'] || ''
      let result = await api.message.index({
        targetableId: this.targetableId,
        targetableType: this.targetableType,
        threadId: threadId.toString()
      })
      this.messages = result[1]
      if (this.toggleCreateThreadBtn) this.toggleCreateThreadBtn(!!this.messages.length)
      this.messageCount = result[0].totalMessage
      this.limitMessage = result[0].limitMessage

      // Load available AI agents
      try {
        this.availableAgents = await api.aiTutorAgent.index({
          targetableId: this.targetableId,
          targetableType: this.targetableType
        })
        // Set default agent
        const defaultAgent = this.availableAgents.find(agent => agent.is_default)
        this.selectedAgentId = defaultAgent ? defaultAgent.id : (this.availableAgents.length > 0 ? this.availableAgents[0].id : null)
      } catch (e) {
        console.error('Failed to load AI agents:', e)
      }

      this.createChannel()
      // TODO: Channel send double time
      this.chanel.send({
        action: 'follow',
        data: {
          targetable_id: this.targetableId,
          targetable_type: this.targetableType
        }
      })
    } catch (e) {}
  }

  async createThread() {
    try {
      const threadForm = new ThreadForm()
      threadForm.targetableId = parseInt(this.targetableId)
      threadForm.targetableType = this.targetableType

      let thread = await api.thread.create(threadForm)

      this.$router
          .push({
            query: { ...this.$route.query, thread_id: thread.id.toString() }
          })
          .catch((error) => {
            if (error.name != 'NavigationDuplicated') {
              throw error
            }
          })
    } catch (e) {}
  }

  async mounted() {
    const container = this.$refs.chatContainer as HTMLElement
    container.addEventListener('wheel', (e) => {
      // Clear existing timer
      if (this.wheelDebounceTimer) {
        clearTimeout(this.wheelDebounceTimer)
      }
      
      // Set new timer with 100ms delay
      this.wheelDebounceTimer = setTimeout(() => {
        if (container.scrollHeight - container.scrollTop < container.clientHeight + 10) {
          console.log("disableScroll false")
          this.disableScroll = false
        } else {
          console.log("disableScroll true")
          this.disableScroll = true
        }
      }, 100)
    })

    document.getElementById('chat-input').addEventListener('input', this.autoExpandTextarea);

    (this.$refs.chatInput as HTMLTextAreaElement).addEventListener('paste', e => {
      e.preventDefault()
      if (e.clipboardData.files.length) {
        (this.$refs.file as HTMLInputElement).files = e.clipboardData.files
        this.handleFileChange()
      } else {
        navigator.clipboard
            .readText()
            .then(cliptext => this.chatContent += cliptext);
      }
    });

    document.addEventListener("click", this.handleCopy)
    document.addEventListener("click", this.handleZoomHighlight)
    document.addEventListener("click", this.handleZoomImage)

    this.$nextTick(function () {
      setTimeout(() => { this.scrollToLast() }, 250)
    })

    this.aiChatQuestions = await api.aiChatQuestionApi.index({context: this.chatQuestionContext(), targetableId: this.targetableId, targetableType: this.targetableType})
  }

  updated() {
    document.getElementById('chat-input').dispatchEvent(new Event("input"))
  }

  beforeDestroy() {
    document.removeEventListener("click", this.handleCopy)
    document.removeEventListener("click", this.handleZoomHighlight)
  }

  async refresh() {
    this.reconnectChannel()
    const threadId = this.$route.query['thread_id'] || ''
    let result = await api.message.index({
      targetableId: this.targetableId,
      targetableType: this.targetableType,
      threadId: threadId.toString()
    })
    this.messages = result[1]
    if (this.toggleCreateThreadBtn) {
      this.toggleCreateThreadBtn(!!this.messages.length)
    }
    this.messageCount = result[0].totalMessage
    this.limitMessage = result[0].limitMessage
    console.log('Component reloaded')
  }

  autoExpandTextarea() {
    const textarea = document.getElementById('chat-input') as HTMLTextAreaElement;
    if (textarea) {
      textarea.setAttribute('style', 'height: auto !important');

      const newHeight = textarea.scrollHeight + 2;

      const maxHeight = 200;

      // Apply the height with !important
      textarea.setAttribute('style', `height: ${Math.min(newHeight, maxHeight)}px !important; overflow-y: ${newHeight > maxHeight ? 'auto' : 'hidden'} !important;`);
    }
  }

  handleFileChange() {
    Array.from((this.$refs.file as HTMLInputElement).files).forEach((file) => {
      if (this.attachments.find((attachment) => attachment.file.name === file.name && attachment.file.size === file.size)) {
        return
      }

      const attachment = {
        isDeleted: false,
        isUploadCompleted: false,
        serverId: null,
        progressPercentage: 5,
        file: file as File
      }

      if (/png|jpeg|jpg/.test(attachment.file.type) && (10 * 1024 * 1024 < attachment.file.size)) {
        alert("Limit 10 MB per image.")
        return
      }
      // } else if (20 * 1024 * 1024 < attachment.file.size) {
      //   alert("Limit 20 MB per file.")
      //   return
      // }

      if (/png|jpeg|jpg/.test(attachment.file.type)) {
        this.attachments.push(attachment)
        this.handleUpload(attachment)
      }
    })
  }

  dragover(e) {
    if (this.usingDeepseek) return
    e.preventDefault()
    this.isDragging = true
  }

  dragleave() {
    if (this.usingDeepseek) return
    this.isDragging = false
  }

  drop(e) {
    if (this.usingDeepseek) return
    (this.$refs.file as HTMLInputElement)
        .files = e.dataTransfer.files
    this.handleFileChange()
    this.isDragging = false
    e.preventDefault()
  }

  getFileName(attachment) {
    return this.getFileNameFromString(attachment.file.name)
  }

  getFileNameFromString(string) {
    return string.replace(/\.[^/.]+$/, "")
  }

  getFileExt(attachment) {
    return this.getFileExtFromString(attachment.file.name)
  }

  getFileExtFromString(string) {
    return string.split('.').pop().toUpperCase()
  }

  addAttachment() {
    (this.$refs.file as HTMLInputElement).click()
  }

  async handleUpload(attachment) {
    const attachmentForm = new AttachmentForm()
    attachmentForm.filename = attachment.file.name

    const attachmentRes = await api.attachment.create(attachmentForm)

    const parseProgress = (progressEvent) => {
      const progressPercentage =
          (progressEvent.loaded / progressEvent.total) * 100;
      if (progressPercentage > 98) {
        attachment.progressPercentage = 98
      } else if (progressPercentage > 5) {
        attachment.progressPercentage = progressPercentage
      }
    }

    const res = await Axios.put(attachmentRes.uploadUrl, attachment.file, {
      headers: {
        'Content-Type': attachment.file.type,
        'Content-Disposition': `attachment; filename=${attachment.file.name}`
      },
      onUploadProgress: parseProgress
    })

    if (res.status === 200) {
      attachment.isUploadCompleted = true
      attachment.serverId = attachmentRes.id
    } else {
      return alert("Error: Upload failed!")
    }


  }

  deleteAttachment(index) {
    this.attachments[index].isDeleted = true
  }

  openUrl(url) {
    window.open(url, "_blank")
  }

  canSubmit() {
    return (this.chatContent.trim() != "" || this.attachments.filter(attachment => !attachment.isDeleted && attachment.isUploadCompleted).length)
           && this.messageCount < this.limitMessage
           && this.hasAvailableAgent;
  }

  handleCopy(e) {
    if (e.target.classList.contains("highlight-copy-btn")) {
      navigator.clipboard.writeText(e.target.closest(".highlight-container").querySelector("code").innerText)
      e.target.querySelector(".action-name").innerText = "コピーしました！"

      setTimeout(() => {
        e.target.querySelector(".action-name").innerText = "コードをコピーする"
      }, 1000)
    }
  }

  handleZoomHighlight(e) {
    if (e.target.classList.contains("highlight-zoom-btn")) {
      this.dialogHTML = e.target.closest(".highlight-container").outerHTML
      this.codeDialog = true
    }
  }

  handleZoomImage(e) {
    if (e.target.classList.contains("message-attachment-image")) {
      this.dialogHTML = e.target.outerHTML
      this.codeDialog = true
    }
  }

  handleEnterChatForm(e) {
    if (e.keyCode == 13 && !e.shiftKey) {
      e.preventDefault()
      if (this.canSubmit()) this.createMessage()
      return false
    }
  }

  copyMessage(index: number) {
    navigator.clipboard.writeText(this.messages[index].contentRaw)
  }

  async regenerate(index: number) {
    this.stopStream()
    await api.message.regenerate(this.messages[index].id)
    this.threadId = this.$route.query['thread_id'] ? String(this.$route.query['thread_id']) : this.threadId
    const result = await api.message.index({
      targetableId: this.targetableId,
      targetableType: this.targetableType,
      threadId: this.threadId
    })
    this.messages = result[1]
    if (this.toggleCreateThreadBtn) {
      this.toggleCreateThreadBtn(!!this.messages.length)
    }
    this.messageCount = result[0].totalMessage
    this.limitMessage = result[0].limitMessage
  }

  speechMedia: HTMLAudioElement = null

  async speech(index: number,) {
    this.stopStream()
    const message = this.messages[index]
    message.speechPlaying = true

    try {
      if (!message.cachedSpeechUrl) {
        const speechData = await api.message.speech(message.id)
        message.cachedSpeechUrl = speechData.data.url
      }

      this.speechMedia = this.$refs.speechMedia as HTMLAudioElement
      this.speechMedia.setAttribute("src", message.cachedSpeechUrl)
      this.speechMedia.addEventListener("ended", () => {
        message.speechPlaying = false
      })

      await this.speechMedia.play()
    } catch (error) {
      this.speechMedia = null
      message.speechPlaying = false
      console.error('Error streaming audio:', error)
    }
  }

  stopStream() {
    document.querySelectorAll(".streaming-indicator").forEach(function(el) { (el as HTMLElement).remove() })

    if (this.speechMedia) {
      try {
        this.speechMedia.pause()
      } catch (error) {}

      this.messages.forEach((message) => { message.speechPlaying = false })
    }

    this.speechMedia = null
  }

  chatQuestionContext() {
    switch (this.targetableType) {
      case "Lesson":
        return ["lesson"]
      case "Exam":
        return ["question"]
      case "Question":
        return ["question"]
      case "UserGoal":
        return ["goal"]
      default:
        return []
    }
  }

  sendSuggestQuestion(aiChatQuestion: AiChatQuestion) {
    if (this.inProgress) return

    this.createMessage({aiChatQuestionId: aiChatQuestion.id})
  }
}

</script>

<style lang="scss">

.streaming-indicator {
  color: #28a745;
  font-family: none;
  animation: blink2 1s infinite;
  animation-play-state: running;
}
@keyframes blink2 {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

.textarea-container {
  min-height: 30px; /* Adjust based on your needs */
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  padding: 0 3px 3px;
}

.chat-container-textarea > .d-flex {
  background: #fff;
}

#chat-input {
  min-height: 30px; /* Adjust based on your needs */
  max-height: 200px;
  box-sizing: border-box;
  padding: 5px;
  width: 100%;
  resize: none;
  border: none;
  outline: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  height: 100%;
}

.chat-details {
  img {
    max-width: 100%;
    margin: 1rem 0;
  }

  p {
    font-size: 1rem;
    margin: 2rem 0;
  }
}

#message-input-section {
  margin-top: -8px;
  background-color: #fff;
}

.chat-input-container {
  background-color: #eee;
  border-radius: 26px;
  padding: 8px;
}

.chat-input-container .v-icon {
  width: 38px;
  height: 36px;
}

.chat-input-attachment {
  background-color: #fff;
  width: 250px;
  padding: 6px;
  border: 1px solid #e3e3e3;
  border-radius: 6px;
}

.chat-input-attachment-title, .chat-input-attachment-ext {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 185px;
}

.chat-input-backdrop {
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  vertical-align: middle;
  display: flex;
  justify-content: center;
}

.chat-input-backdrop * {
  pointer-events: none;
}

.chat-input-backdrop .v-image {
  width: 60px !important;
  height: 60px !important;
}

.chat-input-backdrop h3 {
  margin: 0.2em 1em !important;
  font-weight: 700;
  font-size: 1.5rem !important;
}

.chat-input-backdrop h4 {
  font-size: 1rem !important;
}

.chat-input-attachment-container {
  position: relative;
  width: 250px;
}

.chat-input-progress-bar {
  position: absolute;
  display: block;
  height: 4px;
  bottom: 0;
  left: 0;
  width: 15%;
  background-color: #FCBC51;
  border-radius: 0 0 0px 6px;
  background-image:
      linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
  transition : width 0.5s ease;
}

.chat-input-attachment-container:hover .chat-input-remove-icon {
  display: unset;
}

.chat-input-remove-icon {
  position: absolute;
  top: -6px;
  right: -6px;
  display: none;
}

.chat-input-remove-icon button {
  width: 20px !important;
  height: 20px !important;
  background-color: #fff;
}

.chat-input-remove-icon .v-icon {
  font-size: 16px !important;
}

.highlight-container {
  background-color: #0d0d0d;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  width: 100%;
}
.highlight-container .highlight-header {
  background-color: #2f2f2f;
  height: 32px;
  display: flex;
  color: #cdcdcd;
  border-radius: 6px 6px 0 0;
  font-size: 12px;
  padding: 0 10px;
}
.highlight-container .highlight-header .highlight-lang {
  margin-right: auto;
}
.highlight-container .highlight-header .highlight-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}
.highlight-container .highlight-header .highlight-actions button {
  display: flex;
  flex-direction: row;
  gap: 4px !important;
  align-content: center;
  flex-wrap: wrap;
}
.highlight-container .highlight-header .highlight-actions button:hover {
  color: #fff;
}
.highlight-container .highlight-header .highlight-actions button i {
  font-size: 18px;
}
.highlight-container .highlight-header .highlight-actions button * {
  pointer-events: none;
}
.highlight-container .highlight-header .highlight-actions div:first-child {
  padding: 0 10px;
  border-right: solid 2px hsla(0, 0%, 100%, .15);
}
.highlight-container pre.highlight {
  overflow-y: auto !important;
  overflow-x: auto;
  margin-bottom: 0;
  border-radius: 0 0 6px 6px;
  max-width: 400px;
}
.highlight-container pre.highlight code {
  padding: 0 !important;
  font-size: 16px;
}

.v-dialog .highlight-actions div:first-child  {
  padding: unset !important;
  border-right: unset !important;
}

.v-dialog .highlight-actions .highlight-zoom-btn {
  display: none !important;
}

.chat-container p {
  margin: 1em 0 !important;
}

.suggestion-question-list {
  position: absolute;
  left: 50%;
  top: 40%;
  transform: translate(-50%,-50%);
  width: 100%;
}

.suggestion-question {
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, .1);
  width: 160px;
  height: 124px;
  padding: 10px;
  margin: 8px 6px;
  text-align: left;
  cursor: pointer;
}

.suggestion-question:hover {
  background-color: #f9f9f9;
}

.suggestion-question-icon i, .suggestion-question-icon img {
  height: 20px;
  margin: 0 0 14px 0 !important;
  color: #00bcd4;
  border: unset !important;
}

.suggestion-question-title {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

</style>

<style lang="scss" scoped>
/* Chats container styling */
.chat-container {
  overflow-y: scroll;
}

:where(.chat-container, textarea)::-webkit-scrollbar {
  width: 6px;
}

:where(.chat-container, textarea)::-webkit-scrollbar-track {
  background: #f7f7f8;
  border-radius: 25px;
}

:where(.chat-container, textarea)::-webkit-scrollbar-thumb {
  background: #a9a9bc;
  border-radius: 25px;
}

.default-text {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 70vh;
  padding: 0 10px;
  text-align: center;
  color: #343541;
}

.default-text h1 {
  font-size: 3.3rem;
}

.default-text p {
  font-size: 1.1rem;
}

.chat-container .chat {
  padding: 0px 10px;
  display: flex;
  justify-content: center;
  color: #343541;
}

.chat-container .chat.outgoing {
  background: #ffffff;
  border: 1px solid #ffffff;
}

.chat-container .chat.incoming {
  background: #f7f7f8;
  border-top: 1px solid #d9d9e3;
  border-bottom: 1px solid #d9d9e3;
}

.chat .chat-content {
  display: flex;
  width: 100%;
  align-items: flex-start;
  justify-content: space-between;
  font-size: 1rem;
}

span.material-symbols-rounded {
  user-select: none;
  cursor: pointer;
}

.chat:hover .chat-content:not(:has(.typing-animation), :has(.error)) span {
  visibility: visible;
}

.chat .chat-details {
  display: flex;
  align-items: center;
  width: 100%;
}

.chat .chat-details img {
  width: 35px;
  height: 35px;
  align-self: flex-start;
  object-fit: cover;
  border-radius: 2px;
}

.chat .chat-details p {
  white-space: pre-wrap;
  font-size: 1.05rem;
  padding: 0 50px 0 25px;
  color: #343541;
  word-break: break-word;
}

.chat .chat-details p.error {
  color: #e55865;
}

.chat .typing-animation {
  padding-left: 25px;
  display: inline-flex;
}

.typing-animation .typing-dot {
  height: 7px;
  width: 7px;
  border-radius: 50%;
  margin: 0 3px;
  opacity: 0.7;
  background: #0513ab;
}

.typing-animation .dot1 {
  animation: animateDots 1.5s 0.2s ease-in-out infinite;
}

.typing-animation .dot2 {
  animation: animateDots 1.5s 0.3s ease-in-out infinite;
}

.typing-animation .dot3 {
  animation: animateDots 1.5s 0.4s ease-in-out infinite;
}

.message-content {
  max-width: 90%;
  padding-left: 25px;
  overflow-wrap: break-word;
  width: 100%;
}

.chat.outgoing .message-content {
  white-space: pre-line;
  line-break: anywhere;
  background-color: #fff;
}

.message-content .chat-input-attachment {
  cursor: pointer;
}

.typing-animation .typing-dot:first-child {
  margin-left: 0;
}

.chat-container .message-attachment-image {
  width: auto !important;
  height: 340px !important;
  margin: 0.5rem auto !important;
  cursor: pointer;
}

.v-dialog .message-attachment-image {
  max-width: 100%;
  max-height: 100%;
  margin: 0 auto;
}


@keyframes animateDots {
  0%,
  44% {
    transform: translateY(0px);
  }

  28% {
    opacity: 0.4;
    transform: translateY(-6px);
  }

  44% {
    opacity: 0.2;
  }
}

/* Typing container styling */
.typing-container {
  width: 100%;
  display: flex;
  padding: 20px 10px;
  justify-content: center;
  background: #ffffff;
  border-top: 1px solid #d9d9e3;
}

.typing-container .typing-content {
  display: flex;
  // max-width: 950px;
  width: 100%;
  align-items: flex-end;
}

.typing-container .typing-textarea {
  width: 100%;
  display: flex;
  position: relative;
}

.typing-textarea textarea {
  resize: none;
  height: 55px;
  width: 100%;
  border: none;
  padding: 15px 45px 15px 20px;
  color: #343541;
  font-size: 1rem;
  border-radius: 4px;
  max-height: 250px;
  overflow-y: auto;
  background: #f7f7f8;
  outline: 1px solid #d9d9e3;
}

.typing-textarea textarea::placeholder {
  color: var(--placeholder-color);
}

.typing-content span {
  width: 55px;
  height: 55px;
  display: flex;
  border-radius: 4px;
  font-size: 1.35rem;
  align-items: center;
  justify-content: center;
  color: #a9a9bc;
}

.typing-textarea span {
  position: absolute;
  right: 0;
  bottom: 0;
  visibility: hidden;
}

.typing-textarea textarea:valid ~ span {
  visibility: visible;
}

.typing-controls {
  display: flex;
}

.typing-controls span {
  margin-left: 7px;
  font-size: 1.4rem;
  background: #f7f7f8;
  outline: 1px solid #d9d9e3;
}

.typing-controls span:hover {
  background: #f1f1f3;
}

/* Reponsive Media Query */
@media screen and (max-width: 600px) {
  .default-text h1 {
    font-size: 2.3rem;
  }

  :where(.default-text p, textarea, .chat p) {
    font-size: 0.95rem !important;
  }

  .chat-container .chat {
    padding: 20px 10px;
  }

  .chat-container .chat img {
    height: 32px;
    width: 32px;
  }

  .chat-container .chat p {
    padding: 0 20px;
  }

  .chat .chat-content:not(:has(.typing-animation), :has(.error)) span {
    visibility: visible;
  }

  .typing-container {
    padding: 15px 10px;
  }

  .typing-textarea textarea {
    height: 45px;
    padding: 10px 40px 10px 10px;
  }

  .typing-content span {
    height: 45px;
    width: 45px;
    margin-left: 5px;
  }
  span.material-symbols-rounded {
    font-size: 1.25rem !important;
  }
}

.embed-exam {
  height: 92vh;
}

.clear-btn {
  float: right;
}

.limit-message {
  float: right;
  padding-right: 10px;
  padding-top: 5px;
}

.message-actions {
  display: inline-flex;
  border: 1px solid rgba(0, 0, 0, .1);
  border-radius: 0.5rem;
  padding: 2px;
  background-color: #fff;
  margin-bottom: .3em;
}

.message-actions .v-icon {
  height: 20px;
  width: 20px;
  font-size: 20px;
}

/* Add this to your existing styles */
.chat-input-container.disabled {
  opacity: 0.6;
  background-color: #f0f0f0;
  cursor: not-allowed;
}

.chat-input-container.disabled textarea {
  cursor: not-allowed;
  background-color: #f0f0f0;
}

.limit-message.limit-reached {
  color: #dc3545;
  font-weight: bold;
}
</style>
