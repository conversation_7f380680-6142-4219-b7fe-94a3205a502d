<template>
  <div id="thread-container" flat class="bg-white d-flex flex-column justify-content-between h-full">
    <ul class="thread-list">
      <template v-for="thread in threads">
        <div :key="thread.id">
          <a @click="clickThread(thread.id)" class="text-decoration-none d-flex justify-space-between align-center">
            {{ thread.createdAt.slice(0, 10) }} {{ thread.name }}
            <v-menu offset-y>
              <template v-slot:activator="{ on, attrs }">
                <v-btn small icon color="indigo" v-bind="attrs" v-on="on" class="me-2">
                  <v-icon dark>mdi-dots-vertical</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item @click="openDeleteDialog(thread)">
                  <v-list-item-title>削除</v-list-item-title>
                </v-list-item>
                <v-list-item @click="openEditDialog(thread)">
                  <v-list-item-title>名前の編集</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </a>
          <li class="divider" role="presentation"></li>
          <hr />
        </div>
      </template>
    </ul>
    <!-- Delete confirm dialog -->
    <v-dialog v-model="showDeleteDialog" persistent max-width="320">
      <v-card>
        <v-card-title>本当に削除してもよろしいですか？</v-card-title>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="green darken-1" text @click="showDeleteDialog = false">
            キャンセル
          </v-btn>
          <v-btn color="green darken-1" text @click="deleteThread()">
            OK
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Edit dialog -->
    <v-dialog v-model="showEditDialog" persistent max-width="350">
      <v-card>
        <v-card-title>スレッド名の編集</v-card-title>
        <v-card-text>
          <input class="form-control" type="text" v-model="threadName" />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="green darken-1" text @click="showEditDialog = false">
            キャンセル
          </v-btn>
          <v-btn color="green darken-1" text @click="editThread()">
            OK
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Component, Prop, Watch } from 'vue-property-decorator'
import Thread from '../../../api/v2/models/thread'
import api from '../../../api/v2/api'
@Component({})
export default class ThreadContainer extends Vue {
  @Prop({ default: 'Lesson' }) targetableType: 'Lesson' | 'UserGoal' | 'School'
  @Prop({ required: true }) targetableId: string
  @Prop({ default: 2 }) parentTab: number
  threads: Thread[] = []
  showDeleteDialog: boolean = false
  showEditDialog: boolean = false
  selectedThread: Thread | null = null
  threadName: string = ''
  async created() {
    try {
      this.threads = await api.thread.index({ targetableId: this.targetableId, targetableType: this.targetableType })
    } catch (e) {}
  }
  clickThread(id: number) {
    this.$emit('updateParentTab', this.parentTab)
    this.$router
        .push({
          query: { ...this.$route.query, thread_id: id.toString() }
        })
        .catch((error) => {
          if (error.name != 'NavigationDuplicated') {
            throw error
          }
        })
  }

  openDeleteDialog(thread: Thread) {
    this.selectedThread = thread
    this.showDeleteDialog = true
  }
  openEditDialog(thread: Thread) {
    this.selectedThread = thread
    this.threadName = thread.name
    this.showEditDialog = true
  }
  async deleteThread() {
    try {
      await api.thread.delete(this.selectedThread.id)
      this.threads = this.threads.filter((thread) => thread.id !== this.selectedThread.id)
      this.showDeleteDialog = false
    } catch (e) {}
  }
  async editThread() {
    try {
      await api.thread.update(this.selectedThread.id, { name: this.threadName })
      this.selectedThread.name = this.threadName
      this.showEditDialog = false
    } catch (e) {}
  }
}
</script>

<style lang="scss">
.thread-list {
  overflow-y: auto;
  margin-bottom: 0;
}
.chat-details {
  .highlight {
    overflow-y: auto;
  }
  img {
    max-width: 100%;
    margin: 1rem 0;
  }
  p {
    font-size: 1rem;
    margin: 2rem 0;
  }
}
</style>
